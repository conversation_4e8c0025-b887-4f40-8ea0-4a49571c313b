import { contextBridge, ipc<PERSON>enderer, webUtils } from 'electron'
import { IPC_EVENTS } from '../main/constants/ipc-events'

// 安全的API定义 - 只暴露必要的功能
const secureAPI = {
  // 窗口控制API
  window: {
    minimize: () => ipcRenderer.send(IPC_EVENTS.MINIMIZE),
    maximize: () => ipcRenderer.send(IPC_EVENTS.MAXIMIZE),
    close: () => ipcRenderer.send(IPC_EVENTS.CLOSE)
  },

  // 文件操作API
  file: {
    selectVideo: (): Promise<string | null> => ipcRenderer.invoke(IPC_EVENTS.SELECT_FILE),
    selectVideos: (): Promise<string[]> => ipcRenderer.invoke(IPC_EVENTS.SELECT_FILES),
    selectAudio: (): Promise<string | null> => ipcRenderer.invoke(IPC_EVENTS.SELECT_AUDIO),
    getPathForFile: (file: File): string => {
      try {
        return webUtils.getPathForFile(file)
      } catch (error) {
        console.error('获取文件路径失败:', error)
        return ''
      }
    },
    getFileUrl: (filePath: string): Promise<string | null> => 
      ipcRenderer.invoke(IPC_EVENTS.GET_FILE_URL, filePath),
    selectFolder: (): Promise<string | null> => ipcRenderer.invoke(IPC_EVENTS.SELECT_FOLDER),
    getDefaultOutputPath: (): Promise<string> =>
      ipcRenderer.invoke(IPC_EVENTS.GET_DEFAULT_OUTPUT_PATH),
    ensureOutputDirectory: (outputPath: string, folderName: string): Promise<boolean> =>
      ipcRenderer.invoke(IPC_EVENTS.ENSURE_OUTPUT_DIRECTORY, outputPath, folderName),
    processVideo: (
      filePath: string,
      operation: string,
      outputPath?: string,
      outputFolderName?: string,
      options?: any
    ): Promise<boolean> =>
      ipcRenderer.invoke(
        IPC_EVENTS.PROCESS_VIDEO,
        filePath,
        operation,
        outputPath,
        outputFolderName,
        options
      )
  },

  // 应用功能API
  app: {
    ping: () => ipcRenderer.send(IPC_EVENTS.PING),
    getVersion: (): Promise<string> => ipcRenderer.invoke(IPC_EVENTS.GET_VERSION),
    openFolder: (folderPath: string): Promise<{ success: boolean; error?: string }> =>
      ipcRenderer.invoke(IPC_EVENTS.OPEN_FOLDER, folderPath),
    setHardwareAcceleration: (enabled: boolean): Promise<boolean> =>
      ipcRenderer.invoke(IPC_EVENTS.SET_HARDWARE_ACCELERATION, enabled),
    getSystemInfo: (): Promise<any> => ipcRenderer.invoke(IPC_EVENTS.GET_SYSTEM_INFO),
    setMaxConcurrentTasks: (maxTasks: number): Promise<boolean> =>
      ipcRenderer.invoke(IPC_EVENTS.SET_MAX_CONCURRENT_TASKS, maxTasks),
    getTaskStatus: (): Promise<any> => ipcRenderer.invoke(IPC_EVENTS.GET_TASK_STATUS)
  },

  // 进度监听API
  progress: {
    onUpdate: (callback: (data: { taskId: string; progress: number; status: string; message?: string }) => void) => {
      ipcRenderer.on(IPC_EVENTS.PROGRESS_UPDATE, (_, data) => callback(data))
    },
    onComplete: (callback: (data: { taskId: string }) => void) => {
      ipcRenderer.on(IPC_EVENTS.PROGRESS_COMPLETE, (_, data) => callback(data))
    },
    onError: (callback: (data: { taskId: string; error: string }) => void) => {
      ipcRenderer.on(IPC_EVENTS.PROGRESS_ERROR, (_, data) => callback(data))
    },
    removeAllListeners: () => {
      ipcRenderer.removeAllListeners(IPC_EVENTS.PROGRESS_UPDATE)
      ipcRenderer.removeAllListeners(IPC_EVENTS.PROGRESS_COMPLETE)
      ipcRenderer.removeAllListeners(IPC_EVENTS.PROGRESS_ERROR)
    }
  }
}

// 类型定义
export type SecureAPI = typeof secureAPI

// 暴露安全API到渲染进程
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', secureAPI)
  } catch (error) {
    console.error('Failed to expose secure API:', error)
  }
} else {
  // @ts-ignore
  window.electronAPI = secureAPI
}
