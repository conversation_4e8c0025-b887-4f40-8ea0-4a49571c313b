import { createHashRouter, Navigate } from 'react-router-dom'
import Layout from '../components/Layout'
import HomePage from '../pages/HomePage'
import VideoMutePage from '../pages/VideoMutePage'
import VideoToAudioPage from '../pages/VideoToAudioPage'
import ReplaceAudioPage from '../pages/ReplaceAudioPage'
import CompressVideoPage from '../pages/CompressVideoPage'
import ChangeSpeedPage from '../pages/ChangeSpeedPage'
import VideoEditPage from '../pages/VideoEditPage'
import SettingsPage from '../pages/SettingsPage'

export const router = createHashRouter([
  {
    path: '/',
    element: <Layout />,
    children: [
      {
        index: true,
        element: <HomePage />
      },
      {
        path: 'video-mute',
        element: <VideoMutePage />
      },
      {
        path: 'video-to-audio',
        element: <VideoToAudioPage />
      },
      {
        path: 'replace-audio',
        element: <ReplaceAudioPage />
      },
      {
        path: 'compress-video',
        element: <CompressVideoPage />
      },
      {
        path: 'change-speed',
        element: <ChangeSpeedPage />
      },
      {
        path: 'video-edit',
        element: <VideoEditPage />
      },
      {
        path: 'settings',
        element: <SettingsPage />
      },
      {
        path: '*',
        element: <Navigate to="/" replace />
      }
    ]
  }
])

// 路由路径常量
export const ROUTES = {
  HOME: '/',
  VIDEO_MUTE: '/video-mute',
  VIDEO_TO_AUDIO: '/video-to-audio',
  REPLACE_AUDIO: '/replace-audio',
  COMPRESS_VIDEO: '/compress-video',
  CHANGE_SPEED: '/change-speed',
  VIDEO_EDIT: '/video-edit',
  SETTINGS: '/settings'
} as const