import { useState, useEffect } from 'react'
import * as appService from '../services/app-service'

interface VersionBadgeProps {
  className?: string
  showDetails?: boolean
}

/**
 * 版本信息徽章组件
 * 显示应用版本信息，支持详细信息展示
 */
export function VersionBadge({ className = '', showDetails = false }: VersionBadgeProps) {
  const [versionInfo, setVersionInfo] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const loadVersionInfo = async () => {
      try {
        const info = await appService.getDetailedVersionInfo()
        setVersionInfo(info)
      } catch (error) {
        console.error('获取版本信息失败:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadVersionInfo()
  }, [])

  if (isLoading) {
    return (
      <div className={`inline-flex items-center px-2 py-1 text-xs rounded ${className}`}>
        <div className="animate-pulse bg-gray-300 h-3 w-12 rounded"></div>
      </div>
    )
  }

  if (!versionInfo) {
    return (
      <div className={`inline-flex items-center px-2 py-1 text-xs rounded bg-gray-500 text-white ${className}`}>
        Unknown
      </div>
    )
  }

  const isDev = versionInfo.environment === 'development'
  const badgeColor = isDev ? 'bg-orange-500' : 'bg-green-500'

  if (!showDetails) {
    return (
      <div className={`inline-flex items-center px-2 py-1 text-xs rounded text-white ${badgeColor} ${className}`}>
        {versionInfo.formattedVersion}
      </div>
    )
  }

  return (
    <div className={`inline-block ${className}`}>
      <div className={`inline-flex items-center px-2 py-1 text-xs rounded text-white ${badgeColor} mb-1`}>
        {versionInfo.formattedVersion}
      </div>
      <div className="text-xs text-gray-500 space-y-1">
        <div>构建: {versionInfo.formattedBuildDate}</div>
        {versionInfo.gitCommit && (
          <div>提交: {versionInfo.gitCommit.substring(0, 7)}</div>
        )}
        {versionInfo.gitBranch && (
          <div>分支: {versionInfo.gitBranch}</div>
        )}
      </div>
    </div>
  )
}

export default VersionBadge