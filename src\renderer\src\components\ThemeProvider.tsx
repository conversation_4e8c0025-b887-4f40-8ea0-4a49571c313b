import React, { createContext, useContext, useEffect } from 'react'
import { ConfigProvider } from 'antd'
import { useTheme, ThemeMode } from '../hooks/useTheme'
import { useAppSettings } from '../hooks/useAppSettings'

interface ThemeContextType {
  themeMode: ThemeMode
  isDarkMode: boolean
  setThemeMode: (mode: ThemeMode) => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export const useThemeContext = () => {
  const context = useContext(ThemeContext)
  if (!context) {
    throw new Error('useThemeContext must be used within a ThemeProvider')
  }
  return context
}

interface ThemeProviderProps {
  children: React.ReactNode
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const { settings, saveSettings } = useAppSettings()
  const { themeMode, isDarkMode, setThemeMode, getAntdThemeConfig } = useTheme()

  // 从设置中加载主题
  useEffect(() => {
    if (settings.theme && settings.theme !== themeMode) {
      setThemeMode(settings.theme as ThemeMode)
    }
  }, [settings.theme, themeMode, setThemeMode])

  // 主题切换处理
  const handleThemeChange = (mode: ThemeMode) => {
    setThemeMode(mode)
    saveSettings({ theme: mode })
  }

  const contextValue: ThemeContextType = {
    themeMode,
    isDarkMode,
    setThemeMode: handleThemeChange
  }

  return (
    <ThemeContext.Provider value={contextValue}>
      <ConfigProvider theme={getAntdThemeConfig()}>
        {children}
      </ConfigProvider>
    </ThemeContext.Provider>
  )
}