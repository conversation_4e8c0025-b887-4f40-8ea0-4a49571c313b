import { dialog, BrowserWindow } from 'electron'
import { promises as fs } from 'fs'
import path from 'path'
import os from 'os'
import { addVideoTask } from './task-manager'

/**
 * 选择视频文件
 */
export const selectVideoFile = async (): Promise<string | null> => {
  const window = BrowserWindow.getFocusedWindow()
  if (!window) return null

  const result = await dialog.showOpenDialog(window, {
    title: '选择视频文件',
    filters: [
      {
        name: '视频文件',
        extensions: ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm']
      },
      {
        name: '所有文件',
        extensions: ['*']
      }
    ],
    properties: ['openFile']
  })

  if (result.canceled || result.filePaths.length === 0) {
    return null
  }

  return result.filePaths[0]
}

/**
 * 选择多个视频文件
 */
export const selectVideoFiles = async (): Promise<string[]> => {
  const window = BrowserWindow.getFocusedWindow()
  if (!window) return []

  const result = await dialog.showOpenDialog(window, {
    title: '选择视频文件',
    filters: [
      {
        name: '视频文件',
        extensions: ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm']
      },
      {
        name: '所有文件',
        extensions: ['*']
      }
    ],
    properties: ['openFile', 'multiSelections']
  })

  if (result.canceled || result.filePaths.length === 0) {
    return []
  }

  return result.filePaths
}

/**
 * 选择音频文件
 */
export const selectAudioFile = async (): Promise<string | null> => {
  const window = BrowserWindow.getFocusedWindow()
  if (!window) return null

  const result = await dialog.showOpenDialog(window, {
    title: '选择音频文件',
    filters: [
      {
        name: '音频文件',
        extensions: ['mp3', 'wav', 'aac', 'flac', 'm4a', 'ogg', 'wma']
      },
      {
        name: '所有文件',
        extensions: ['*']
      }
    ],
    properties: ['openFile']
  })

  if (result.canceled || result.filePaths.length === 0) {
    return null
  }

  return result.filePaths[0]
}

/**
 * 选择输出文件夹
 */
export const selectOutputFolder = async (): Promise<string | null> => {
  const window = BrowserWindow.getFocusedWindow()
  if (!window) return null

  const result = await dialog.showOpenDialog(window, {
    title: '选择输出文件夹',
    properties: ['openDirectory']
  })

  if (result.canceled || result.filePaths.length === 0) {
    return null
  }

  return result.filePaths[0]
}

/**
 * 确保输出目录存在
 */
export const ensureOutputDirectory = async (outputPath: string, folderName: string): Promise<string> => {
  const fullOutputPath = path.join(outputPath, folderName)
  
  try {
    await fs.access(fullOutputPath)
  } catch (error) {
    // 目录不存在，创建它
    await fs.mkdir(fullOutputPath, { recursive: true })
    console.log(`创建输出目录: ${fullOutputPath}`)
  }
  
  return fullOutputPath
}

/**
 * 处理视频文件 - 使用任务队列管理
 */
export const processVideo = async (
  filePath: string, 
  operation: string, 
  outputPath?: string, 
  outputFolderName?: string,
  options?: {
    audioPath?: string
    volume?: number
    speed?: number
    quality?: string
  }
): Promise<boolean> => {
  try {
    console.log(`添加视频处理任务: ${filePath}, 操作: ${operation}`)
    
    // 将任务添加到队列中，由任务管理器控制并发数量
    const success = await addVideoTask(filePath, operation, outputPath, outputFolderName, options)
    
    return success
  } catch (error) {
    console.error('视频处理失败:', error)
    return false
  }
}

/**
 * 获取默认输出路径（用户桌面）
 */
export const getDefaultOutputPath = (): string => {
  try {
    // 获取用户桌面路径
    const desktopPath = path.join(os.homedir(), 'Desktop')
    return desktopPath
  } catch (error) {
    console.error('获取桌面路径失败:', error)
    // 如果获取桌面路径失败，使用用户主目录
    return os.homedir()
  }
}

/**
 * 获取视频文件信息
 */
export const getVideoInfo = async (filePath: string) => {
  // 这里后续可以使用FFprobe获取视频信息
  return {
    path: filePath,
    duration: 0,
    width: 0,
    height: 0,
    size: 0
  }
}
