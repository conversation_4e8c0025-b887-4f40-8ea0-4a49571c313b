/**
 * 进度管理器
 * 用于跟踪和报告视频处理进度
 */

interface ProgressInfo {
  taskId: string
  progress: number
  status: 'processing' | 'completed' | 'error'
  message?: string
}

class ProgressManager {
  private progressMap: Map<string, ProgressInfo> = new Map()
  private listeners: Map<string, (progress: ProgressInfo) => void> = new Map()

  /**
   * 更新任务进度
   */
  updateProgress(taskId: string, progress: number, status: 'processing' | 'completed' | 'error' = 'processing', message?: string) {
    const progressInfo: ProgressInfo = {
      taskId,
      progress: Math.min(100, Math.max(0, progress)),
      status,
      message
    }

    this.progressMap.set(taskId, progressInfo)
    
    // 通知监听器
    const listener = this.listeners.get(taskId)
    if (listener) {
      listener(progressInfo)
    }

    console.log(`任务 ${taskId} 进度: ${progressInfo.progress}% (${status})`)
  }

  /**
   * 获取任务进度
   */
  getProgress(taskId: string): ProgressInfo | null {
    return this.progressMap.get(taskId) || null
  }

  /**
   * 添加进度监听器
   */
  addListener(taskId: string, listener: (progress: ProgressInfo) => void) {
    this.listeners.set(taskId, listener)
  }

  /**
   * 移除进度监听器
   */
  removeListener(taskId: string) {
    this.listeners.delete(taskId)
  }

  /**
   * 清理已完成的任务
   */
  cleanup(taskId: string) {
    this.progressMap.delete(taskId)
    this.listeners.delete(taskId)
  }

  /**
   * 获取所有任务进度
   */
  getAllProgress(): ProgressInfo[] {
    return Array.from(this.progressMap.values())
  }
}

// 创建全局进度管理器实例
export const progressManager = new ProgressManager()

// 导出进度管理器的方法
export const updateTaskProgress = progressManager.updateProgress.bind(progressManager)
export const getTaskProgress = progressManager.getProgress.bind(progressManager)
export const addProgressListener = progressManager.addListener.bind(progressManager)
export const removeProgressListener = progressManager.removeListener.bind(progressManager)
export const cleanupTaskProgress = progressManager.cleanup.bind(progressManager)