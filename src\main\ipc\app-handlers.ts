import { ipcMain } from 'electron'
import { APP_EVENTS } from '../constants/ipc-events'
import * as appService from '../services/app-service'

/**
 * 注册所有应用相关的IPC处理器
 */
export const registerAppHandlers = () => {
  ipcMain.on(APP_EVENTS.PING, appService.handlePing)

  ipcMain.handle(APP_EVENTS.GET_VERSION, async () => {
    const appInfo = appService.getAppInfo()
    return appInfo.version
  })

  ipcMain.handle(APP_EVENTS.OPEN_FOLDER, async (_, folderPath: string) => {
    return await appService.openFolder(folderPath)
  })

  ipcMain.handle(APP_EVENTS.SET_HARDWARE_ACCELERATION, async (_, enabled: boolean) => {
    return appService.setHardwareAcceleration(enabled)
  })

  ipcMain.handle(APP_EVENTS.GET_SYSTEM_INFO, async () => {
    return appService.getSystemInfo()
  })

  ipcMain.handle(APP_EVENTS.SET_MAX_CONCURRENT_TASKS, async (_, maxTasks: number) => {
    return appService.setMaxConcurrentTasks(maxTasks)
  })

  ipcMain.handle(APP_EVENTS.GET_TASK_STATUS, async () => {
    return appService.getTaskStatus()
  })
}

/**
 * 移除所有应用相关的IPC处理器
 */
export const unregisterAppHandlers = () => {
  ipcMain.removeAllListeners(APP_EVENTS.PING)
  ipcMain.removeHandler(APP_EVENTS.GET_VERSION)
  ipcMain.removeHandler(APP_EVENTS.OPEN_FOLDER)
  ipcMain.removeHandler(APP_EVENTS.SET_HARDWARE_ACCELERATION)
  ipcMain.removeHandler(APP_EVENTS.GET_SYSTEM_INFO)
  ipcMain.removeHandler(APP_EVENTS.SET_MAX_CONCURRENT_TASKS)
  ipcMain.removeHandler(APP_EVENTS.GET_TASK_STATUS)
}
