import { useState, useEffect, useCallback } from 'react'
import { theme } from 'antd'

export type ThemeMode = 'dark' | 'light' | 'auto'

interface ThemeConfig {
  algorithm: any
  token: any
  components: any
}

/**
 * 主题管理Hook
 */
export function useTheme() {
  const [themeMode, setThemeMode] = useState<ThemeMode>('dark')
  const [isDarkMode, setIsDarkMode] = useState(true)

  // 检测系统主题
  const detectSystemTheme = useCallback(() => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return true // 默认深色
  }, [])

  // 更新实际主题状态
  const updateTheme = useCallback(
    (mode: ThemeMode) => {
      let shouldUseDark = true

      switch (mode) {
        case 'dark':
          shouldUseDark = true
          break
        case 'light':
          shouldUseDark = false
          break
        case 'auto':
          shouldUseDark = detectSystemTheme()
          break
      }

      setIsDarkMode(shouldUseDark)

      // 更新HTML根元素的class，用于CSS变量
      if (typeof document !== 'undefined') {
        document.documentElement.classList.toggle('dark', shouldUseDark)
        document.documentElement.classList.toggle('light', !shouldUseDark)
      }
    },
    [detectSystemTheme]
  )

  // 监听系统主题变化
  useEffect(() => {
    if (themeMode === 'auto' && typeof window !== 'undefined' && window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handleChange = () => {
        if (themeMode === 'auto') {
          updateTheme('auto')
        }
      }
      mediaQuery.addEventListener('change', handleChange)
      return () => mediaQuery.removeEventListener('change', handleChange)
    }
    return undefined // 明确表示不需要清理
  }, [themeMode, updateTheme])

  // 初始化主题
  useEffect(() => {
    updateTheme(themeMode)
  }, [themeMode, updateTheme])

  // 获取Antd主题配置
  const getAntdThemeConfig = useCallback((): ThemeConfig => {
    const baseConfig = {
      token: {
        colorPrimary: '#8b5cf6',
        borderRadius: 12,
        fontSize: 14
      },
      components: {
        Button: {
          borderRadius: 8,
          fontWeight: 500
        }
      }
    }

    if (isDarkMode) {
      return {
        algorithm: theme.darkAlgorithm,
        token: {
          ...baseConfig.token,
          colorBgBase: '#1f2937',
          colorBgContainer: '#374151',
          colorText: '#f9fafb',
          colorTextSecondary: '#d1d5db'
        },
        components: {
          ...baseConfig.components,
          Notification: {
            colorBgElevated: '#374151',
            colorText: '#f9fafb',
            colorTextHeading: '#f9fafb',
            colorIcon: '#8b5cf6',
            borderRadiusLG: 16
          },
          Modal: {
            colorBgElevated: '#374151',
            colorText: '#f9fafb',
            colorTextHeading: '#f9fafb',
            borderRadiusLG: 16
          }
        }
      }
    } else {
      return {
        algorithm: theme.defaultAlgorithm,
        token: {
          ...baseConfig.token,
          colorBgBase: '#ffffff',
          colorBgContainer: '#f8fafc',
          colorText: '#1f2937',
          colorTextSecondary: '#6b7280'
        },
        components: {
          ...baseConfig.components,
          Notification: {
            colorBgElevated: '#ffffff',
            colorText: '#1f2937',
            colorTextHeading: '#1f2937',
            colorIcon: '#8b5cf6',
            borderRadiusLG: 16
          },
          Modal: {
            colorBgElevated: '#ffffff',
            colorText: '#1f2937',
            colorTextHeading: '#1f2937',
            borderRadiusLG: 16
          }
        }
      }
    }
  }, [isDarkMode])

  return {
    themeMode,
    isDarkMode,
    setThemeMode,
    getAntdThemeConfig
  }
}
