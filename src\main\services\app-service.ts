import { app, shell } from 'electron'
import {
  setMaxConcurrentTasks as setTaskManagerMaxConcurrent,
  getTaskStatus as getTaskManagerStatus
} from './task-manager'

/**
 * 处理ping事件
 */
export const handlePing = (): void => {
  console.log('pong')
}

/**
 * 获取应用信息
 */
export const getAppInfo = () => {
  return {
    name: app.getName(),
    version: app.getVersion(),
    path: app.getAppPath(),
    isPackaged: app.isPackaged
  }
}



/**
 * 退出应用
 */
export const quitApp = (): void => {
  app.quit()
}

/**
 * 重启应用
 */
export const restartApp = (): void => {
  app.relaunch()
  app.exit()
}

/**
 * 设置硬件加速
 */
export const setHardwareAcceleration = (enabled: boolean): boolean => {
  try {
    if (enabled) {
      // 启用硬件加速
      app.commandLine.removeSwitch('disable-gpu')
      app.commandLine.removeSwitch('disable-gpu-sandbox')
      console.log('硬件加速已启用')
    } else {
      // 禁用硬件加速
      app.commandLine.appendSwitch('disable-gpu')
      app.commandLine.appendSwitch('disable-gpu-sandbox')
      console.log('硬件加速已禁用')
    }
    return true
  } catch (error) {
    console.error('设置硬件加速失败:', error)
    return false
  }
}

/**
 * 获取系统性能信息
 */
export const getSystemInfo = () => {
  const os = require('os')
  return {
    platform: process.platform,
    arch: process.arch,
    cpus: os.cpus().length,
    totalMemory: Math.round(os.totalmem() / 1024 / 1024 / 1024), // GB
    freeMemory: Math.round(os.freemem() / 1024 / 1024 / 1024), // GB
    hardwareAcceleration: !app.commandLine.hasSwitch('disable-gpu')
  }
}

/**
 * 设置最大并发任务数
 */
export const setMaxConcurrentTasks = (maxTasks: number): boolean => {
  try {
    console.log(`尝试设置最大并发任务数: ${maxTasks}`)
    setTaskManagerMaxConcurrent(maxTasks)
    console.log(`成功设置最大并发任务数: ${maxTasks}`)
    return true
  } catch (error: any) {
    console.error('设置最大并发任务数失败:', error)
    console.error('错误详情:', error.message, error.stack)
    return false
  }
}

/**
 * 获取任务状态
 */
export const getTaskStatus = () => {
  try {
    return getTaskManagerStatus()
  } catch (error) {
    console.error('获取任务状态失败:', error)
    return {
      maxConcurrent: 2,
      running: 0,
      queued: 0,
      runningTasks: []
    }
  }
}

/**
 * 打开文件夹
 */
export const openFolder = async (
  folderPath: string
): Promise<{ success: boolean; error?: string }> => {
  try {
    // 检查文件夹是否存在
    const fs = require('fs').promises
    try {
      await fs.access(folderPath)
    } catch (error) {
      // 文件夹不存在，尝试创建
      try {
        await fs.mkdir(folderPath, { recursive: true })
        console.log(`创建输出目录: ${folderPath}`)
      } catch (createError) {
        console.error('创建文件夹失败:', createError)
        return { success: false, error: '无法创建输出文件夹' }
      }
    }

    // 打开文件夹
    await shell.openPath(folderPath)
    return { success: true }
  } catch (error) {
    console.error('打开文件夹失败:', error)
    return { success: false, error: '无法打开文件夹' }
  }
}
