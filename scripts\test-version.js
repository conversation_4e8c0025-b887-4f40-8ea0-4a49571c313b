#!/usr/bin/env node

/**
 * 版本管理系统测试脚本
 * 验证版本信息的一致性和正确性
 */

const path = require('path')
const fs = require('fs')

function testVersionConsistency() {
  console.log('🧪 开始测试版本管理系统...\n')

  // 1. 测试package.json版本
  const packageJson = require('../package.json')
  console.log(`📦 package.json版本: ${packageJson.version}`)

  // 2. 测试构建信息文件
  const buildInfoPath = path.join(__dirname, '../src/shared/build-info.json')
  let buildInfo = null
  
  if (fs.existsSync(buildInfoPath)) {
    buildInfo = require(buildInfoPath)
    console.log(`🏗️  构建信息版本: ${buildInfo.version}`)
    console.log(`📅 构建日期: ${buildInfo.buildDate}`)
    console.log(`🌿 Git分支: ${buildInfo.gitBranch || '未知'}`)
    console.log(`📝 Git提交: ${buildInfo.gitCommit ? buildInfo.gitCommit.substring(0, 7) : '未知'}`)
    console.log(`🌍 环境: ${buildInfo.environment}`)
  } else {
    console.log('⚠️  构建信息文件不存在，将使用默认值')
  }

  // 3. 测试版本一致性
  console.log('\n🔍 版本一致性检查:')
  
  if (buildInfo && buildInfo.version !== packageJson.version) {
    console.log('❌ 版本不一致！')
    console.log(`   package.json: ${packageJson.version}`)
    console.log(`   build-info.json: ${buildInfo.version}`)
    return false
  } else {
    console.log('✅ 版本信息一致')
  }

  // 4. 测试版本格式化
  try {
    // 模拟版本格式化逻辑
    const isDev = (buildInfo?.environment || 'development') === 'development'
    let formattedVersion = `v${packageJson.version}`
    
    if (isDev) {
      formattedVersion += '-dev'
    }
    
    if (buildInfo?.gitCommit) {
      formattedVersion += `+${buildInfo.gitCommit.substring(0, 7)}`
    }
    
    console.log(`🎨 格式化版本: ${formattedVersion}`)
  } catch (error) {
    console.log('❌ 版本格式化失败:', error.message)
    return false
  }

  console.log('\n✅ 版本管理系统测试通过！')
  return true
}

// 如果直接运行此脚本
if (require.main === module) {
  const success = testVersionConsistency()
  process.exit(success ? 0 : 1)
}

module.exports = { testVersionConsistency }