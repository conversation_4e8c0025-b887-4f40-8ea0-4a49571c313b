import { useState } from 'react'
import { Select } from 'antd'
import FeaturePage from '../components/FeaturePage'
import { VIDEO_OPERATIONS } from '../services/file-service'

function VideoToAudioPage() {
  const [audioFormat, setAudioFormat] = useState('mp3')
  const [audioQuality, setAudioQuality] = useState('high')

  return (
    <FeaturePage
      title="视频转音频"
      icon="🎵"
      description="提取视频中的音频内容，支持多种音频格式输出"
      operation={VIDEO_OPERATIONS.EXTRACT_AUDIO}
    >
      <div className="space-y-6">
        <div>
          <label className="block text-white/90 text-sm font-medium mb-2">
            输出格式
          </label>
          <Select
            value={audioFormat}
            onChange={setAudioFormat}
            className="w-full"
            size="large"
            options={[
              { value: 'mp3', label: 'MP3' },
              { value: 'wav', label: 'WAV' },
              { value: 'aac', label: 'AAC' },
              { value: 'flac', label: 'FLAC' }
            ]}
          />
        </div>

        <div>
          <label className="block text-white/90 text-sm font-medium mb-2">
            音频质量
          </label>
          <Select
            value={audioQuality}
            onChange={setAudioQuality}
            className="w-full"
            size="large"
            options={[
              { value: 'high', label: '高质量 (320kbps)' },
              { value: 'medium', label: '中等质量 (192kbps)' },
              { value: 'low', label: '低质量 (128kbps)' }
            ]}
          />
        </div>

        <div className="text-sm text-white/60">
          <p>• 支持批量转换多个视频文件</p>
          <p>• 保持原始音频质量</p>
          <p>• 自动检测最佳音频参数</p>
        </div>
      </div>
    </FeaturePage>
  )
}

export default VideoToAudioPage