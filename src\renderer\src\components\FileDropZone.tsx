import React, { useState, useCallback } from 'react'
import * as fileService from '../services/file-service'
import { v4 as uuidv4 } from 'uuid';

interface FileDropZoneProps {
  onFilesAdded: (files: { id: string; path: string; name: string }[]) => void
  existingFiles: string[]
  disabled?: boolean
  className?: string
  maxFiles?: number // 最大文件数量限制，默认为1
}

const FileDropZone: React.FC<FileDropZoneProps> = ({
  onFilesAdded,
  existingFiles,
  disabled = false,
  className = '',
  maxFiles = 1
}) => {
  const [isDragOver, setIsDragOver] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)
  const [dragCounter, setDragCounter] = useState(0)

  // 检查是否已达到文件数量限制
  const isAtMaxFiles = existingFiles.length >= maxFiles
  const isDisabled = disabled || isAtMaxFiles

  // 支持的视频文件扩展名
  const supportedExtensions = ['mp4', 'avi', 'mov', 'mkv', 'wmv', 'flv', 'webm']

  // 检查文件是否为支持的视频格式
  const isVideoFile = (file: File): boolean => {
    const extension = file.name.split('.').pop()?.toLowerCase()
    return extension ? supportedExtensions.includes(extension) : false
  }

  // 过滤重复文件并检查数量限制
  const filterDuplicateFiles = (files: { path: string; name: string }[]): { path: string; name: string }[] => {
    const uniqueFiles = files.filter(file => !existingFiles.includes(file.path))

    // 检查数量限制
    const remainingSlots = maxFiles - existingFiles.length
    if (remainingSlots <= 0) {
      console.warn(`已达到最大文件数量限制 (${maxFiles})`)
      return []
    }

    // 如果超出剩余槽位，只取前面的文件
    if (uniqueFiles.length > remainingSlots) {
      console.warn(`只能添加 ${remainingSlots} 个文件，已达到最大限制 (${maxFiles})`)
      return uniqueFiles.slice(0, remainingSlots)
    }

    return uniqueFiles
  }

  // 处理文件列表
  const processFiles = (files: File[]) => {
    setIsProcessing(true)

    try {
      const videoFiles = files.filter(isVideoFile)

      if (videoFiles.length === 0) {
        console.warn('没有找到支持的视频文件')
        setIsProcessing(false)
        return
      }

      const fileInfos: { path: string; name: string }[] = []

      for (const file of videoFiles) {
        try {
          const filePath = fileService.getPathForFile(file)
          if (filePath) {
            fileInfos.push({
              path: filePath,
              name: file.name
            })
          }
        } catch (error) {
          console.error(`获取文件路径失败: ${file.name}`, error)
        }
      }

      // 过滤重复文件
      const uniqueFiles = filterDuplicateFiles(fileInfos)

      if (uniqueFiles.length === 0) {
        const isAtLimit = existingFiles.length >= maxFiles
        const message = isAtLimit
          ? `已达到最大文件数量限制 (${maxFiles})`
          : '所有文件都已存在，跳过重复文件'
        console.warn(message)
        setIsProcessing(false)
        return
      }

      // 生成文件对象
      const newFiles = uniqueFiles.map(file => ({
        id: `file_${uuidv4()}`,
        path: file.path,
        name: file.name
      }))

      onFilesAdded(newFiles)
    } catch (error) {
      console.error('处理文件失败:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  // 拖拽进入
  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!isDisabled) {
      setDragCounter(prev => {
        const newCount = prev + 1
        if (newCount === 1) {
          setIsDragOver(true)
        }
        return newCount
      })
    }
  }, [isDisabled])

  // 拖拽离开
  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    setDragCounter(prev => {
      const newCount = prev - 1
      if (newCount === 0) {
        setIsDragOver(false)
      }
      return newCount
    })
  }, [])

  // 拖拽悬停
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  // 文件放置
  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // 重置拖拽状态
    setDragCounter(0)
    setIsDragOver(false)

    if (isDisabled || isProcessing) return

    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      processFiles(files)
    }
  }, [isDisabled, isProcessing, existingFiles])

  // 文件选择
  const handleFileSelect = useCallback(async () => {
    if (isDisabled || isProcessing) return

    try {
      const filePaths = await fileService.selectVideoFiles()
      if (filePaths.length > 0) {
        const fileInfos = filePaths.map(filePath => ({
          path: filePath,
          name: filePath.split(/[/\\]/).pop() || filePath
        }))

        // 过滤重复文件
        const uniqueFiles = filterDuplicateFiles(fileInfos)

        if (uniqueFiles.length === 0) {
          const isAtLimit = existingFiles.length >= maxFiles
          const message = isAtLimit
            ? `已达到最大文件数量限制 (${maxFiles})`
            : '所有文件都已存在，跳过重复文件'
          console.warn(message)
          return
        }

        // 生成文件对象
        const newFiles = uniqueFiles.map(file => ({
          id: `file_${uuidv4()}`,
          path: file.path,
          name: file.name
        }))

        onFilesAdded(newFiles)
      }
    } catch (error) {
      console.error('选择文件失败:', error)
    }
  }, [isDisabled, isProcessing, existingFiles, maxFiles])

  return (
    <div
      className={`
        relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300
        ${isDragOver && !isDisabled
          ? 'border-purple-400 bg-purple-500/10'
          : 'border-white/20 bg-white/5'
        }
        ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-white/40'}
        ${className}
      `}
      onDragEnter={isDisabled ? undefined : handleDragEnter}
      onDragLeave={isDisabled ? undefined : handleDragLeave}
      onDragOver={isDisabled ? undefined : handleDragOver}
      onDrop={isDisabled ? undefined : handleDrop}
      onClick={isDisabled ? undefined : handleFileSelect}
    >
      {isProcessing ? (
        <div className="text-white/60">
          <div className="animate-spin w-8 h-8 border-2 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>正在处理文件...</p>
        </div>
      ) : (
        <>
          <div className="text-white/40 mb-4">
            <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          <p className="text-white/60 mb-2">
            {isAtMaxFiles
              ? `已达到最大文件数量限制 (${existingFiles.length}/${maxFiles})`
              : isDragOver
                ? '松开鼠标添加文件'
                : '拖拽视频文件到此处，或点击选择文件'
            }
          </p>
          <p className="text-white/40 text-sm">
            支持格式: MP4, AVI, MOV, MKV, WMV, FLV, WEBM
            {maxFiles > 1 && !isAtMaxFiles && (
              <span className="block mt-1">
                最多可添加 {maxFiles} 个文件 ({existingFiles.length}/{maxFiles})
              </span>
            )}
          </p>
          {isDragOver && (
            <div className="absolute inset-0 bg-purple-500/20 rounded-xl flex items-center justify-center">
              <p className="text-purple-300 font-semibold">松开添加文件</p>
            </div>
          )}
        </>
      )}
    </div>
  )
}

export default FileDropZone