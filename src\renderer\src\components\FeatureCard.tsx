interface FeatureCardProps {
  icon: string
  title: string
  description: string
  onClick: () => void
}

function FeatureCard({ icon, title, description, onClick }: FeatureCardProps) {
  return (
    <div 
      className="bg-white/10 backdrop-blur-md rounded-2xl p-6 cursor-pointer transition-all duration-300 hover:bg-white/20 hover:scale-105 hover:shadow-2xl group"
      onClick={onClick}
    >
      <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
        {icon}
      </div>
      <h3 className="text-xl font-semibold text-white mb-2">{title}</h3>
      <p className="text-white/80 text-sm leading-relaxed">{description}</p>
    </div>
  )
}

export default FeatureCard