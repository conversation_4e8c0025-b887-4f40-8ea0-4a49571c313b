// Preload 安全API类型定义
// 定义暴露给渲染进程的所有API接口

export interface WindowAPI {
  minimize: () => void
  maximize: () => void
  close: () => void
}

export interface FileAPI {
  selectVideo: () => Promise<string | null>
  selectVideos: () => Promise<string[]>
  selectAudio: () => Promise<string | null>
  getPathForFile: (file: File) => string
  getFileUrl: (filePath: string) => Promise<string | null>
  selectFolder: () => Promise<string | null>
  getDefaultOutputPath: () => Promise<string>
  ensureOutputDirectory: (outputPath: string, folderName: string) => Promise<boolean>
  processVideo: (filePath: string, operation: string, outputPath?: string, outputFolderName?: string, options?: any) => Promise<boolean>
}

export interface SystemInfo {
  platform: string
  arch: string
  cpus: number
  totalMemory: number
  freeMemory: number
  hardwareAcceleration: boolean
}

export interface TaskStatus {
  maxConcurrent: number
  running: number
  queued: number
  runningTasks: Array<{
    id: string
    filePath: string
    operation: string
  }>
}

export interface AppAPI {
  ping: () => void
  getVersion: () => Promise<string>
  openFolder: (folderPath: string) => Promise<{ success: boolean; error?: string }>
  setHardwareAcceleration: (enabled: boolean) => Promise<boolean>
  getSystemInfo: () => Promise<SystemInfo>
  setMaxConcurrentTasks: (maxTasks: number) => Promise<boolean>
  getTaskStatus: () => Promise<TaskStatus>
}

export interface ProgressData {
  taskId: string
  progress: number
  status: string
  message?: string
}

export interface ProgressAPI {
  onUpdate: (callback: (data: ProgressData) => void) => void
  onComplete: (callback: (data: { taskId: string }) => void) => void
  onError: (callback: (data: { taskId: string; error: string }) => void) => void
  removeAllListeners: () => void
}

export interface ElectronAPI {
  window: WindowAPI
  file: FileAPI
  app: AppAPI
  progress: ProgressAPI
}

// 扩展全局Window接口，供渲染进程使用
declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}

export {}