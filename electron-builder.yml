appId: com.videoclip.视频剪辑
productName: 视频剪辑
directories:
  buildResources: build
# 压缩优化
compression: maximum
# 排除不必要的文件
includeSubNodeModules: false
files:
  - '!**/.vscode/*'
  - '!src/*'
  - '!electron.vite.config.{js,ts,mjs,cjs}'
  - '!{.eslintcache,eslint.config.mjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}'
  - '!{.env,.env.*,.npmrc,pnpm-lock.yaml}'
  - '!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}'
asarUnpack:
  - resources/**
win:
  executableName: 视频剪辑
  target:
    - target: nsis
      arch:
        - x64
nsis:
  artifactName: 视频剪辑-setup-${version}.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  oneClick: false
  allowToChangeInstallationDirectory: true
  perMachine: false
  installerIcon: build/icon.ico
  uninstallerIcon: build/icon.ico
  installerHeaderIcon: build/icon.ico
  # 允许用户选择是否创建快捷方式
  createDesktopShortcut: "always"
  createStartMenuShortcut: true
  # 支持中文路径和Unicode
  unicode: true
  # 设置默认安装目录为中文路径
  installerLanguages:
    - zh_CN
mac:
  entitlementsInherit: build/entitlements.mac.plist
  extendInfo:
    - NSCameraUsageDescription: Application requests access to the device's camera.
    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
dmg:
  artifactName: ${name}-${version}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
appImage:
  artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish:
  provider: generic
  url: https://example.com/auto-updates
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
