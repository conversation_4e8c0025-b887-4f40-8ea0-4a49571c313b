import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { ROUTES } from '../router'
import * as fileService from '../services/file-service'
import { useNotification } from '../hooks/useNotification'
import { useAppSettings } from '../hooks/useAppSettings'
import FileDropZone from './FileDropZone'

interface FeaturePageProps {
  title: string
  icon: string
  description: string
  operation: string
  children?: React.ReactNode | ((selectedFile: string | null) => React.ReactNode)
  // 额外的处理选项，由具体页面提供
  getProcessOptions?: () => any
  // 验证是否可以开始处理，由具体页面提供
  validateBeforeProcess?: () => { valid: boolean; message?: string }
  // 是否支持实时编辑模式（单文件）
  enableLiveEdit?: boolean
}

interface FileItem {
  id: string
  path: string
  name: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress: number
}

function FeaturePage({ title, icon, description, operation, children, getProcessOptions, validateBeforeProcess, enableLiveEdit }: FeaturePageProps) {
  const navigate = useNavigate()
  const [files, setFiles] = useState<FileItem[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  const { contextHolder, showSuccess, showError, showWarning } = useNotification()
  const { settings, getFullOutputPath } = useAppSettings()

  const handleFilesAdded = (newFiles: { id: string; path: string; name: string }[]) => {
    const fileItems: FileItem[] = newFiles.map(file => ({
      ...file,
      status: 'pending',
      progress: 0
    }))
    setFiles(prev => [...prev, ...fileItems])
  }

  const handleRemoveFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId))
  }

  const handleClearAllFiles = () => {
    setFiles([])
  }

  const handleProcessAll = async () => {
    if (files.length === 0) {
      showWarning('提示', '请先选择要处理的视频文件')
      return
    }

    if (!settings.outputPath) {
      showWarning('提示', '请先在设置页面配置输出路径')
      return
    }

    // 执行自定义验证
    if (validateBeforeProcess) {
      const validation = validateBeforeProcess()
      if (!validation.valid) {
        showWarning('验证失败', validation.message || '请检查设置选项')
        return
      }
    }

    setIsProcessing(true)

    try {
      // 设置进度监听器
      const progressListener = (data: { taskId: string; progress: number; status: string; message?: string }) => {
        console.log('前端收到进度更新:', data)
        
        // 更新对应文件的进度
        setFiles(prev => prev.map(file => {
          // 这里需要根据taskId找到对应的文件，暂时更新所有处理中的文件
          if (file.status === 'processing') {
            return { ...file, progress: data.progress }
          }
          return file
        }))
      }

      // 注册进度监听器
      window.electronAPI?.progress.onUpdate(progressListener)

      // 逐个处理文件
      for (const file of files) {
        if (file.status === 'pending') {
          // 更新文件状态为处理中
          setFiles(prev => prev.map(f => 
            f.id === file.id ? { ...f, status: 'processing' as const, progress: 0 } : f
          ))

          try {
            // 获取额外的处理选项
            const processOptions = getProcessOptions ? getProcessOptions() : undefined
            console.log('FeaturePage - processOptions:', processOptions)
            
            const success = await fileService.processVideo(
              file.path,
              operation,
              settings.outputPath,
              settings.outputFolderName,
              processOptions
            )

            // 更新文件状态
            setFiles(prev => prev.map(f => 
              f.id === file.id 
                ? { ...f, status: success ? 'completed' as const : 'error' as const, progress: success ? 100 : 0 }
                : f
            ))

            if (success) {
              showSuccess('文件处理完成', `${file.name} 处理完成`)
            } else {
              showError('文件处理失败', `${file.name} 处理失败`)
            }
          } catch (error) {
            console.error(`处理文件 ${file.name} 失败:`, error)
            setFiles(prev => prev.map(f => 
              f.id === file.id ? { ...f, status: 'error' as const, progress: 0 } : f
            ))
            showError('处理错误', `${file.name} 处理过程中出现错误`)
          }
        }
      }

      showSuccess('批量处理完成', '所有文件处理完成，请查看输出目录')
    } catch (error) {
      console.error('批量处理失败:', error)
      showError('处理错误', '批量处理过程中出现未知错误，请重试')
    } finally {
      setIsProcessing(false)
      // 清理进度监听器
      window.electronAPI?.progress.removeAllListeners()
    }
  }

  return (
    <>
      <div className="flex flex-col h-full">
        {/* Header */}
        <header className="flex items-center justify-between px-6 py-4 bg-white/5 backdrop-blur-sm">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate(ROUTES.HOME)}
              className="text-white/70 hover:text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
            <div className="flex items-center space-x-3">
              <div className="text-3xl">{icon}</div>
              <h1 className="text-2xl font-bold text-white">{title}</h1>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <div className="flex-1 p-8 overflow-y-auto">
          <div className="max-w-4xl mx-auto">
            {/* Description */}
            <div className="text-center mb-8">
              <p className="text-xl text-white/80">{description}</p>
            </div>

            {/* File Selection */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 mb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-white">文件管理</h3>
                {files.length > 0 && (
                  <button
                    onClick={handleClearAllFiles}
                    disabled={isProcessing}
                    className="bg-red-500 text-white px-4 py-2 rounded-lg font-semibold hover:bg-red-600 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    清空列表
                  </button>
                )}
              </div>
              
              {/* 拖拽上传区域 */}
              <FileDropZone
                onFilesAdded={handleFilesAdded}
                existingFiles={files.map(f => f.path)}
                disabled={isProcessing}
                className="mb-4"
              />
              
              {/* 文件列表 */}
              {files.length > 0 && (
                <div className="space-y-3">
                  <h4 className="text-sm font-medium text-white/80 mb-2">
                    已选择 {files.length} 个文件
                  </h4>
                  {files.map((file) => (
                    <div key={file.id} className="p-4 bg-white/10 rounded-xl">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex-1 min-w-0">
                          <p className="text-white/90 text-sm font-medium truncate">{file.name}</p>
                          <p className="text-white/60 text-xs truncate">{file.path}</p>
                        </div>
                        <div className="flex items-center space-x-2 ml-4">
                          <div className={`px-2 py-1 rounded text-xs font-medium ${
                            file.status === 'pending' ? 'bg-gray-500 text-white' :
                            file.status === 'processing' ? 'bg-blue-500 text-white' :
                            file.status === 'completed' ? 'bg-green-500 text-white' :
                            'bg-red-500 text-white'
                          }`}>
                            {file.status === 'pending' ? '等待中' :
                             file.status === 'processing' ? '处理中' :
                             file.status === 'completed' ? '已完成' : '失败'}
                          </div>
                          <button
                            onClick={() => handleRemoveFile(file.id)}
                            disabled={isProcessing && file.status === 'processing'}
                            className="text-red-400 hover:text-red-300 disabled:opacity-50 disabled:cursor-not-allowed"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        </div>
                      </div>
                      {file.status === 'processing' && (
                        <div className="mt-2">
                          <div className="flex justify-between text-xs text-white/80 mb-1">
                            <span>处理进度</span>
                            <span>{file.progress}%</span>
                          </div>
                          <div className="w-full bg-white/20 rounded-full h-1.5">
                            <div 
                              className="bg-gradient-to-r from-purple-500 to-pink-500 h-1.5 rounded-full transition-all duration-300"
                              style={{ width: `${file.progress}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Custom Settings */}
            {children && (
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 mb-6">
                <h3 className="text-lg font-semibold text-white mb-4">
                  {enableLiveEdit ? '编辑器' : '设置选项'}
                </h3>
                {typeof children === 'function' 
                  ? children(files.length > 0 ? files[0].path : null)
                  : children
                }
              </div>
            )}

            {/* Output Path Info */}
            {settings.outputPath && (
              <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6 mb-6">
                <h3 className="text-lg font-semibold text-white mb-4">输出设置</h3>
                <div className="p-4 bg-white/10 rounded-xl">
                  <p className="text-white/90 text-sm mb-2">处理后的文件将保存到:</p>
                  <p className="text-white font-mono text-xs break-all">{getFullOutputPath()}</p>
                </div>
              </div>
            )}

            {/* Process Section */}
            <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6">
              <h3 className="text-lg font-semibold text-white mb-4">开始处理</h3>
              
              {!settings.outputPath && (
                <div className="mb-4 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-xl">
                  <p className="text-yellow-400 text-sm">
                    ⚠️ 未设置输出路径，请先在设置页面配置输出目录
                  </p>
                </div>
              )}
              
              {isProcessing && (
                <div className="mb-4 p-4 bg-blue-500/10 border border-blue-500/20 rounded-xl">
                  <p className="text-blue-400 text-sm mb-2">
                    🔄 正在批量处理文件，请耐心等待...
                  </p>
                  <div className="text-xs text-white/60">
                    已处理: {files.filter(f => f.status === 'completed').length} / {files.length} 个文件
                  </div>
                </div>
              )}

              <button
                onClick={handleProcessAll}
                disabled={files.length === 0 || isProcessing || !settings.outputPath}
                className={`w-full py-3 rounded-xl font-semibold transition-all duration-300 ${
                  files.length === 0 || isProcessing || !settings.outputPath
                    ? 'bg-gray-500 text-gray-300 cursor-not-allowed'
                    : 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 transform hover:scale-105'
                }`}
              >
                {isProcessing ? '处理中...' : `开始${title}`}
              </button>
              
              {!settings.outputPath && (
                <div className="mt-3 text-center">
                  <button
                    onClick={() => navigate(ROUTES.SETTINGS)}
                    className="text-purple-400 hover:text-purple-300 text-sm underline"
                  >
                    前往设置页面配置输出路径
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Antd Notification Context */}
      {contextHolder}
    </>
  )
}

export default FeaturePage