import { useState, useEffect, useCallback } from 'react'
import { DEFAULT_SETTINGS, STORAGE_KEYS } from '../constants/app-config'
import * as fileService from '../services/file-service'

export interface AppSettings {
  outputPath: string
  outputFolderName: string
  autoOpenOutput: boolean
  hardwareAcceleration: boolean
  maxConcurrentTasks: number
  theme: string
}

/**
 * 全局应用设置管理Hook
 */
export function useAppSettings() {
  const [settings, setSettings] = useState<AppSettings>(DEFAULT_SETTINGS)
  const [isLoading, setIsLoading] = useState(true)

  // 从localStorage加载设置
  const loadSettings = useCallback(async () => {
    try {
      const savedSettings = localStorage.getItem(STORAGE_KEYS.APP_SETTINGS)
      
      if (savedSettings) {
        const parsed = JSON.parse(savedSettings)
        
        // 如果已保存的设置中outputPath为空，也要获取默认桌面路径
        if (!parsed.outputPath) {
          try {
            const defaultPath = await fileService.getDefaultOutputPath()
            const updatedSettings = { ...DEFAULT_SETTINGS, ...parsed, outputPath: defaultPath }
            setSettings(updatedSettings)
            // 更新localStorage中的设置
            localStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(updatedSettings))
            // 自动创建输出目录
            await fileService.ensureOutputDirectory(defaultPath, updatedSettings.outputFolderName)
          } catch (error) {
            console.error('获取默认桌面路径失败:', error)
            setSettings({ ...DEFAULT_SETTINGS, ...parsed })
          }
        } else {
          setSettings({ ...DEFAULT_SETTINGS, ...parsed })
          // 确保输出目录存在
          await fileService.ensureOutputDirectory(parsed.outputPath, parsed.outputFolderName)
        }
      } else {
        // 首次启动，获取默认桌面路径
        try {
          const defaultPath = await fileService.getDefaultOutputPath()
          const settingsWithDefaultPath = { ...DEFAULT_SETTINGS, outputPath: defaultPath }
          setSettings(settingsWithDefaultPath)
          // 保存默认设置到localStorage
          localStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(settingsWithDefaultPath))
          // 自动创建输出目录
          await fileService.ensureOutputDirectory(defaultPath, settingsWithDefaultPath.outputFolderName)
        } catch (error) {
          console.error('获取默认桌面路径失败:', error)
          setSettings(DEFAULT_SETTINGS)
        }
      }
    } catch (error) {
      console.error('加载设置失败:', error)
      setSettings(DEFAULT_SETTINGS)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // 保存设置到localStorage
  const saveSettings = useCallback((newSettings: Partial<AppSettings>) => {
    try {
      const updatedSettings = { ...settings, ...newSettings }
      setSettings(updatedSettings)
      localStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(updatedSettings))
      return true
    } catch (error) {
      console.error('保存设置失败:', error)
      return false
    }
  }, [settings])

  // 重置设置到默认值
  const resetSettings = useCallback(async () => {
    try {
      // 重置时也要获取默认桌面路径
      const defaultPath = await fileService.getDefaultOutputPath()
      const settingsWithDefaultPath = { ...DEFAULT_SETTINGS, outputPath: defaultPath }
      setSettings(settingsWithDefaultPath)
      localStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(settingsWithDefaultPath))
      return true
    } catch (error) {
      console.error('重置设置失败:', error)
      setSettings(DEFAULT_SETTINGS)
      localStorage.setItem(STORAGE_KEYS.APP_SETTINGS, JSON.stringify(DEFAULT_SETTINGS))
      return false
    }
  }, [])

  // 获取完整的输出路径（包含自定义文件夹）
  const getFullOutputPath = useCallback(() => {
    if (!settings.outputPath) {
      return null
    }
    // 使用适当的路径分隔符
    const separator = settings.outputPath.includes('\\') ? '\\' : '/'
    return `${settings.outputPath}${separator}${settings.outputFolderName}`
  }, [settings.outputPath, settings.outputFolderName])

  // 初始化时加载设置
  useEffect(() => {
    loadSettings()
  }, [loadSettings])

  return {
    settings,
    isLoading,
    saveSettings,
    resetSettings,
    getFullOutputPath,
    loadSettings
  }
}