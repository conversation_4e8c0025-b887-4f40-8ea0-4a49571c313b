import React from 'react'
import * as windowService from '../services/window-service'
import logo from '@renderer/assets/logo.png'

function CustomTitleBar() {
  const handleMinimize = () => {
    windowService.minimizeWindow()
  }

  const handleMaximize = () => {
    windowService.maximizeWindow()
  }

  const handleClose = () => {
    windowService.closeWindow()
  }

  return (
    <div
      className="flex items-center justify-between h-8 bg-gray-800 text-white select-none"
      style={{ WebkitAppRegion: 'drag' } as React.CSSProperties}
    >
      {/* 左侧：应用图标和标题 */}
      <div className="flex items-center px-3 space-x-2">
        <div className="w-4 h-4 bg-gradient-to-r from-purple-400 to-pink-400 rounded-sm flex items-center justify-center">
          <img src={logo} alt="" />
        </div>
        <span className="text-sm font-medium">视频剪辑</span>
      </div>

      {/* 右侧：窗口控制按钮 */}
      <div className="flex" style={{ WebkitAppRegion: 'no-drag' } as React.CSSProperties}>
        <button
          onClick={handleMinimize}
          className="w-12 h-8 flex items-center justify-center hover:bg-gray-700 transition-colors"
        >
          <svg className="w-3 h-3" viewBox="0 0 12 12" fill="currentColor">
            <rect x="2" y="5" width="8" height="2" />
          </svg>
        </button>
        <button
          onClick={handleMaximize}
          className="w-12 h-8 flex items-center justify-center hover:bg-gray-700 transition-colors"
        >
          <svg className="w-3 h-3" viewBox="0 0 12 12" fill="currentColor">
            <rect x="2" y="2" width="8" height="8" stroke="currentColor" strokeWidth="1" fill="none" />
          </svg>
        </button>
        <button
          onClick={handleClose}
          className="w-12 h-8 flex items-center justify-center hover:bg-red-600 transition-colors"
        >
          <svg className="w-3 h-3" viewBox="0 0 12 12" fill="currentColor">
            <path d="M2.5 2.5L9.5 9.5M9.5 2.5L2.5 9.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
          </svg>
        </button>
      </div>
    </div>
  )
}

export default CustomTitleBar