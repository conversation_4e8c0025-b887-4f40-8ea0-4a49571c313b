import { ipcMain } from 'electron'
import { FILE_EVENTS } from '../constants/ipc-events'
import * as fileService from '../services/file-service'

/**
 * 注册所有文件相关的IPC处理器
 */
export const registerFileHandlers = () => {
  ipcMain.handle(FILE_EVENTS.SELECT_FILE, async () => {
    return await fileService.selectVideoFile()
  })

  ipcMain.handle(FILE_EVENTS.SELECT_FILES, async () => {
    return await fileService.selectVideoFiles()
  })

  ipcMain.handle(FILE_EVENTS.SELECT_AUDIO, async () => {
    return await fileService.selectAudioFile()
  })

  ipcMain.handle(FILE_EVENTS.SELECT_FOLDER, async () => {
    return await fileService.selectOutputFolder()
  })

  ipcMain.handle(FILE_EVENTS.GET_DEFAULT_OUTPUT_PATH, async () => {
    return fileService.getDefaultOutputPath()
  })

  ipcMain.handle(FILE_EVENTS.ENSURE_OUTPUT_DIRECTORY, async (_, outputPath: string, folderName: string) => {
    try {
      await fileService.ensureOutputDirectory(outputPath, folderName)
      return true
    } catch (error) {
      console.error('确保输出目录存在失败:', error)
      return false
    }
  })

  ipcMain.handle(FILE_EVENTS.PROCESS_VIDEO, async (_, filePath: string, operation: string, outputPath?: string, outputFolderName?: string, options?: any) => {
    return await fileService.processVideo(filePath, operation, outputPath, outputFolderName, options)
  })

  ipcMain.handle(FILE_EVENTS.GET_FILE_URL, async (_, filePath: string) => {
    try {
      const fs = await import('fs')
      const path = await import('path')

      // 检查文件是否存在
      if (!fs.existsSync(filePath)) {
        console.error('File does not exist:', filePath)
        return null
      }

      // 规范化路径
      const normalizedPath = path.resolve(filePath)

      // 尝试使用自定义协议（更安全）
      try {
        const customUrl = `local-file://${encodeURIComponent(normalizedPath)}`
        console.log('Generated custom protocol URL:', customUrl)
        return customUrl
      } catch (customError) {
        console.log('Custom protocol failed, falling back to file:// protocol')

        // 备用方案：使用 file:// 协议
        const { pathToFileURL } = await import('url')
        const fileUrl = pathToFileURL(normalizedPath).href

        console.log('Original file path:', filePath)
        console.log('Normalized path:', normalizedPath)
        console.log('Generated file URL:', fileUrl)

        return fileUrl
      }
    } catch (error) {
      console.error('Failed to generate file URL:', error)
      console.error('Error details:', error)
      return null
    }
  })
}

/**
 * 移除所有文件相关的IPC处理器
 */
export const unregisterFileHandlers = () => {
  ipcMain.removeHandler(FILE_EVENTS.SELECT_FILE)
  ipcMain.removeHandler(FILE_EVENTS.SELECT_FILES)
  ipcMain.removeHandler(FILE_EVENTS.SELECT_AUDIO)
  ipcMain.removeHandler(FILE_EVENTS.SELECT_FOLDER)
  ipcMain.removeHandler(FILE_EVENTS.GET_DEFAULT_OUTPUT_PATH)
  ipcMain.removeHandler(FILE_EVENTS.ENSURE_OUTPUT_DIRECTORY)
  ipcMain.removeHandler(FILE_EVENTS.PROCESS_VIDEO)
  ipcMain.removeHandler(FILE_EVENTS.GET_FILE_URL)
}