import { BrowserWindow } from 'electron'

/**
 * 获取当前聚焦的窗口
 */
const getFocusedWindow = (): BrowserWindow | null => {
  return BrowserWindow.getFocusedWindow()
}

/**
 * 最小化窗口
 */
export const minimizeWindow = (): void => {
  const window = getFocusedWindow()
  if (window) {
    window.minimize()
  }
}

/**
 * 切换窗口最大化状态
 */
export const toggleMaximizeWindow = (): void => {
  const window = getFocusedWindow()
  if (window) {
    if (window.isMaximized()) {
      window.unmaximize()
    } else {
      window.maximize()
    }
  }
}

/**
 * 关闭窗口
 */
export const closeWindow = (): void => {
  const window = getFocusedWindow()
  if (window) {
    window.close()
  }
}

/**
 * 获取窗口状态信息
 */
export const getWindowState = () => {
  const window = getFocusedWindow()
  if (!window) return null

  return {
    isMaximized: window.isMaximized(),
    isMinimized: window.isMinimized(),
    isFullScreen: window.isFullScreen(),
    bounds: window.getBounds()
  }
}