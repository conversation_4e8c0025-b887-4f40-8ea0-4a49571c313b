/**
 * 版本信息管理
 * 统一管理应用版本、构建信息等
 */

// 从package.json读取版本信息
import packageJson from '../../package.json'

export interface VersionInfo {
  version: string
  buildDate: string
  buildTime: string
  gitCommit?: string
  gitBranch?: string
  environment: 'development' | 'production'
}

/**
 * 获取构建信息（如果存在）
 */
const getBuildInfo = (): Partial<VersionInfo> => {
  try {
    // 尝试读取构建时生成的信息
    const buildInfo = require('./build-info.json')
    return buildInfo
  } catch (error) {
    // 如果构建信息不存在，返回空对象
    return {}
  }
}

/**
 * 获取版本信息
 */
export const getVersionInfo = (): VersionInfo => {
  const buildInfo = getBuildInfo()
  const now = new Date()
  
  return {
    version: buildInfo.version || packageJson.version,
    buildDate: buildInfo.buildDate || now.toISOString().split('T')[0], // YYYY-MM-DD
    buildTime: buildInfo.buildTime || now.toISOString(),
    gitCommit: buildInfo.gitCommit || process.env.GIT_COMMIT_HASH || undefined,
    gitBranch: buildInfo.gitBranch || process.env.GIT_BRANCH || undefined,
    environment: (buildInfo.environment as 'development' | 'production') || 
                 (process.env.NODE_ENV === 'production' ? 'production' : 'development')
  }
}

/**
 * 格式化版本显示
 */
export const formatVersion = (versionInfo: VersionInfo): string => {
  let version = `v${versionInfo.version}`
  
  if (versionInfo.environment === 'development') {
    version += '-dev'
  }
  
  if (versionInfo.gitCommit) {
    version += `+${versionInfo.gitCommit.substring(0, 7)}`
  }
  
  return version
}

/**
 * 格式化构建日期
 */
export const formatBuildDate = (buildDate: string): string => {
  const date = new Date(buildDate)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

// 导出当前版本信息
export const VERSION_INFO = getVersionInfo()