/**
 * 应用服务 - 使用安全API
 */

/**
 * 发送ping消息到主进程
 */
export const pingApp = (): void => {
  try {
    window.electronAPI?.app.ping()
  } catch (error) {
    console.error('Ping失败:', error)
  }
}

/**
 * 获取应用版本
 */
export const getAppVersion = async (): Promise<string> => {
  try {
    const version = await window.electronAPI?.app.getVersion()
    return version || 'Unknown'
  } catch (error) {
    console.error('获取版本失败:', error)
    return 'Unknown'
  }
}

/**
 * 获取详细版本信息（前端直接处理构建信息）
 */
export const getDetailedVersionInfo = async () => {
  try {
    // 从主进程获取最新的版本信息（这个始终是最新的，来自 package.json）
    const currentVersion = await getAppVersion()
    
    // 前端直接处理构建信息
    let buildInfo: any = null
    try {
      // 尝试导入构建信息，添加时间戳避免缓存
      const timestamp = Date.now()
      buildInfo = await import(`../../../shared/build-info.json?t=${timestamp}`)
      // 处理 ES 模块的 default 导出
      buildInfo = buildInfo.default || buildInfo
    } catch (error) {
      // 构建信息不存在时使用默认值
      console.log('构建信息文件不存在，使用默认值')
    }
    
    const now = new Date()
    const versionInfo = {
      version: currentVersion, // 优先使用从主进程获取的最新版本
      buildDate: buildInfo?.buildDate || now.toISOString().split('T')[0],
      buildTime: buildInfo?.buildTime || now.toISOString(),
      gitCommit: buildInfo?.gitCommit,
      gitBranch: buildInfo?.gitBranch,
      environment: buildInfo?.environment || 'development',
      appVersion: currentVersion
    }
    
    // 格式化版本显示
    let formattedVersion = `v${versionInfo.version}`
    if (versionInfo.environment === 'development') {
      formattedVersion += '-dev'
    }
    if (versionInfo.gitCommit) {
      formattedVersion += `+${versionInfo.gitCommit.substring(0, 7)}`
    }
    
    // 格式化构建日期
    const buildDate = new Date(versionInfo.buildDate)
    const formattedBuildDate = buildDate.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
    
    return {
      ...versionInfo,
      formattedVersion,
      formattedBuildDate
    }
  } catch (error) {
    console.error('获取详细版本信息失败:', error)
    return null
  }
}

/**
 * 打开文件夹
 */
export const openFolder = async (folderPath: string): Promise<{ success: boolean; error?: string }> => {
  try {
    const result = await window.electronAPI?.app.openFolder(folderPath)
    return result || { success: false, error: '调用失败' }
  } catch (error) {
    console.error('打开文件夹失败:', error)
    return { success: false, error: '调用失败' }
  }
}

/**
 * 设置硬件加速
 */
export const setHardwareAcceleration = async (enabled: boolean): Promise<boolean> => {
  try {
    const result = await window.electronAPI?.app.setHardwareAcceleration(enabled)
    return result || false
  } catch (error) {
    console.error('设置硬件加速失败:', error)
    return false
  }
}

/**
 * 获取系统信息
 */
export const getSystemInfo = async () => {
  try {
    const result = await window.electronAPI?.app.getSystemInfo()
    return result || null
  } catch (error) {
    console.error('获取系统信息失败:', error)
    return null
  }
}

/**
 * 设置最大并发任务数
 */
export const setMaxConcurrentTasks = async (maxTasks: number): Promise<boolean> => {
  try {
    const result = await window.electronAPI?.app.setMaxConcurrentTasks(maxTasks)
    return result || false
  } catch (error) {
    console.error('设置最大并发任务数失败:', error)
    return false
  }
}

/**
 * 获取任务状态
 */
export const getTaskStatus = async () => {
  try {
    const result = await window.electronAPI?.app.getTaskStatus()
    return result || null
  } catch (error) {
    console.error('获取任务状态失败:', error)
    return null
  }
}