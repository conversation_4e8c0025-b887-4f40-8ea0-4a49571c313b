import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>lide<PERSON>, But<PERSON> } from 'antd'
import FeaturePage from '../components/FeaturePage'
import { VIDEO_OPERATIONS } from '../services/file-service'

function ReplaceAudioPage() {
  const [audioFile, setAudioFile] = useState<string | null>(null)
  const [volumeLevel, setVolumeLevel] = useState(100)

  const handleSelectAudio = async () => {
    try {
      const selectedAudio = await window.electronAPI?.file.selectAudio()
      if (selectedAudio) {
        setAudioFile(selectedAudio)
      }
    } catch (error) {
      console.error('选择音频文件失败:', error)
    }
  }

  // 提供处理选项
  const getProcessOptions = () => {
    const options = {
      audioPath: audioFile,
      volume: volumeLevel / 100 // 转换为0-2的范围
    }
    console.log('ReplaceAudioPage - getProcessOptions:', options)
    return options
  }

  // 验证是否可以开始处理
  const validateBeforeProcess = () => {
    if (!audioFile) {
      return { valid: false, message: '请先选择音频文件' }
    }
    return { valid: true }
  }

  return (
    <FeaturePage
      title="更换背景音乐"
      icon="🎼"
      description="替换视频原有音轨，添加您喜欢的背景音乐"
      operation={VIDEO_OPERATIONS.REPLACE_AUDIO}
      getProcessOptions={getProcessOptions}
      validateBeforeProcess={validateBeforeProcess}
    >
      <div className="space-y-6">
        <div>
          <label className="block text-white/90 text-sm font-medium mb-2">
            选择音频文件
          </label>
          {audioFile ? (
            <div className="p-3 bg-white/10 rounded-lg mb-2">
              <p className="text-white/90 text-sm">已选择: {audioFile}</p>
            </div>
          ) : (
            <div className="p-6 border-2 border-dashed border-white/20 rounded-lg text-center">
              <p className="text-white/60 text-sm">请选择音频文件</p>
            </div>
          )}
          <Button
            onClick={handleSelectAudio}
            className="mt-2"
            size="large"
          >
            选择音频文件
          </Button>
        </div>

        <div>
          <label className="block text-white/90 text-sm font-medium mb-2">
            音量调节: {volumeLevel}%
          </label>
          <Slider
            min={0}
            max={200}
            value={volumeLevel}
            onChange={setVolumeLevel}
            marks={{
              0: '静音',
              100: '正常',
              200: '增强'
            }}
            tooltip={{ formatter: (value) => `${value}%` }}
          />
        </div>

        <Checkbox>
          <span className="text-white/90">添加淡入淡出效果</span>
        </Checkbox>

        <div className="text-sm text-white/60">
          <p>• 支持 MP3、WAV、AAC 等音频格式</p>
          <p>• 自动调整音频长度匹配视频</p>
          <p>• 可设置循环播放或截断</p>
        </div>
      </div>
    </FeaturePage>
  )
}

export default ReplaceAudioPage