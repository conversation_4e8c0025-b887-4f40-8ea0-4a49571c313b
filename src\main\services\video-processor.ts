import ffmpeg from 'fluent-ffmpeg'
import ffmpegInstaller from '@ffmpeg-installer/ffmpeg'
import path from 'path'
// import { promises as fs } from 'fs' // 暂时注释掉未使用的导入

// 设置FFmpeg路径
ffmpeg.setFfmpegPath(ffmpegInstaller.path)

export interface VideoProcessOptions {
  inputPath: string
  outputPath: string
  operation: string
  onProgress?: (progress: number) => void
  onError?: (error: string) => void
  onComplete?: () => void
}

/**
 * 视频处理器类
 */
export class VideoProcessor {
  /**
   * 视频静音处理
   */
  static async muteVideo(options: VideoProcessOptions): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        console.log(`开始视频静音处理: ${options.inputPath}`)
        
        const command = ffmpeg(options.inputPath)
          .noAudio() // 移除音频轨道
          .output(options.outputPath)
          .on('start', (commandLine) => {
            console.log('FFmpeg命令:', commandLine)
          })
          .on('progress', (progress) => {
            const percent = Math.round(progress.percent || 0)
            console.log(`处理进度: ${percent}%`)
            options.onProgress?.(percent)
          })
          .on('end', () => {
            console.log('视频静音处理完成')
            options.onComplete?.()
            resolve(true)
          })
          .on('error', (err) => {
            console.error('视频静音处理失败:', err.message)
            options.onError?.(err.message)
            reject(new Error(`视频静音处理失败: ${err.message}`))
          })

        // 开始处理
        command.run()
      } catch (error) {
        console.error('视频静音处理异常:', error)
        reject(error)
      }
    })
  }

  /**
   * 视频转音频处理
   */
  static async extractAudio(options: VideoProcessOptions & { audioFormat?: string }): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        console.log(`开始视频转音频处理: ${options.inputPath}`)
        
        const audioFormat = options.audioFormat || 'mp3'
        const command = ffmpeg(options.inputPath)
          .noVideo() // 只保留音频
          .audioCodec('libmp3lame') // 使用MP3编码
          .format(audioFormat)
          .output(options.outputPath)
          .on('start', (commandLine) => {
            console.log('FFmpeg命令:', commandLine)
          })
          .on('progress', (progress) => {
            const percent = Math.round(progress.percent || 0)
            console.log(`处理进度: ${percent}%`)
            options.onProgress?.(percent)
          })
          .on('end', () => {
            console.log('视频转音频处理完成')
            options.onComplete?.()
            resolve(true)
          })
          .on('error', (err) => {
            console.error('视频转音频处理失败:', err.message)
            options.onError?.(err.message)
            reject(new Error(`视频转音频处理失败: ${err.message}`))
          })

        // 开始处理
        command.run()
      } catch (error) {
        console.error('视频转音频处理异常:', error)
        reject(error)
      }
    })
  }

  /**
   * 视频压缩处理
   */
  static async compressVideo(options: VideoProcessOptions & {
    quality?: string
    compressionLevel?: string
    targetSize?: number
    maintainQuality?: boolean
  }): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        console.log(`开始视频压缩处理: ${options.inputPath}`)
        console.log(`压缩参数:`, {
          compressionLevel: options.compressionLevel,
          targetSize: options.targetSize,
          maintainQuality: options.maintainQuality
        })

        const compressionLevel = options.compressionLevel || 'medium'
        const maintainQuality = options.maintainQuality !== false

        let crf = 23
        let preset = 'medium'
        let maxBitrate = ''
        let scaleFilter = ''

        // 根据压缩级别设置参数
        switch (compressionLevel) {
          case 'light':
            crf = maintainQuality ? 20 : 18
            preset = 'slow'
            maxBitrate = '2000k'
            break
          case 'medium':
            crf = maintainQuality ? 25 : 23
            preset = 'medium'
            maxBitrate = '1000k'
            scaleFilter = 'scale=trunc(iw*0.8/2)*2:trunc(ih*0.8/2)*2' // 降低20%分辨率
            break
          case 'heavy':
            crf = maintainQuality ? 30 : 32
            preset = 'fast'
            maxBitrate = '500k'
            scaleFilter = 'scale=trunc(iw*0.6/2)*2:trunc(ih*0.6/2)*2' // 降低40%分辨率
            break
          case 'custom':
            if (options.targetSize) {
              // 根据目标大小计算比特率 (粗略估算)
              const targetBitrate = Math.floor(options.targetSize * 8 / 60) // 假设1分钟视频
              maxBitrate = `${targetBitrate}k`
              crf = 28
              preset = 'fast'
              scaleFilter = 'scale=trunc(iw*0.7/2)*2:trunc(ih*0.7/2)*2'
            }
            break
        }

        const command = ffmpeg(options.inputPath)
          .videoCodec('libx264')
          .audioCodec('aac')
          .addOption('-crf', crf.toString())
          .addOption('-preset', preset)
          .addOption('-movflags', '+faststart') // 优化网络播放

        // 添加比特率限制
        if (maxBitrate) {
          command.addOption('-maxrate', maxBitrate)
          command.addOption('-bufsize', `${parseInt(maxBitrate) * 2}k`)
        }

        // 添加分辨率缩放
        if (scaleFilter) {
          command.videoFilters(scaleFilter)
        }

        // 音频压缩
        command.addOption('-b:a', '128k') // 限制音频比特率

        command
          .output(options.outputPath)
          .on('start', (commandLine) => {
            console.log('FFmpeg命令:', commandLine)
          })
          .on('progress', (progress) => {
            const percent = Math.round(progress.percent || 0)
            console.log(`处理进度: ${percent}%`)
            options.onProgress?.(percent)
          })
          .on('end', () => {
            console.log('视频压缩处理完成')
            options.onComplete?.()
            resolve(true)
          })
          .on('error', (err) => {
            console.error('视频压缩处理失败:', err.message)
            options.onError?.(err.message)
            reject(new Error(`视频压缩处理失败: ${err.message}`))
          })

        // 开始处理
        command.run()
      } catch (error) {
        console.error('视频压缩处理异常:', error)
        reject(error)
      }
    })
  }

  /**
   * 视频调速处理
   */
  static async changeVideoSpeed(options: VideoProcessOptions & { speed?: number }): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        console.log(`开始视频调速处理: ${options.inputPath}`)
        
        const speed = options.speed || 1.0
        const videoFilter = `setpts=${1/speed}*PTS`
        const audioFilter = `atempo=${speed}`

        const command = ffmpeg(options.inputPath)
          .videoFilters(videoFilter)
          .audioFilters(audioFilter)
          .output(options.outputPath)
          .on('start', (commandLine) => {
            console.log('FFmpeg命令:', commandLine)
          })
          .on('progress', (progress) => {
            const percent = Math.round(progress.percent || 0)
            console.log(`处理进度: ${percent}%`)
            options.onProgress?.(percent)
          })
          .on('end', () => {
            console.log('视频调速处理完成')
            options.onComplete?.()
            resolve(true)
          })
          .on('error', (err) => {
            console.error('视频调速处理失败:', err.message)
            options.onError?.(err.message)
            reject(new Error(`视频调速处理失败: ${err.message}`))
          })

        // 开始处理
        command.run()
      } catch (error) {
        console.error('视频调速处理异常:', error)
        reject(error)
      }
    })
  }

  /**
   * 视频音频替换处理
   */
  static async replaceAudio(options: VideoProcessOptions & { audioPath: string; volume?: number }): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        console.log(`开始视频音频替换处理: ${options.inputPath}`)
        console.log(`音频文件: ${options.audioPath}`)
        
        const volume = options.volume || 1.0
        
        const command = ffmpeg()
          .input(options.inputPath) // 视频输入
          .input(options.audioPath) // 音频输入
          .videoCodec('copy') // 复制视频流，不重新编码
          .audioCodec('aac') // 音频编码为AAC
          .audioFilters(`volume=${volume}`) // 设置音量
          .outputOptions([
            '-map', '0:v', // 使用第一个输入的视频流
            '-map', '1:a', // 使用第二个输入的音频流
            '-shortest' // 以最短的流为准（如果音频比视频短，视频会被截断）
          ])
          .output(options.outputPath)
          .on('start', (commandLine) => {
            console.log('FFmpeg命令:', commandLine)
          })
          .on('progress', (progress) => {
            const percent = Math.round(progress.percent || 0)
            console.log(`处理进度: ${percent}%`)
            options.onProgress?.(percent)
          })
          .on('end', () => {
            console.log('视频音频替换处理完成')
            options.onComplete?.()
            resolve(true)
          })
          .on('error', (err) => {
            console.error('视频音频替换处理失败:', err.message)
            options.onError?.(err.message)
            reject(new Error(`视频音频替换处理失败: ${err.message}`))
          })

        // 开始处理
        command.run()
      } catch (error) {
        console.error('视频音频替换处理异常:', error)
        reject(error)
      }
    })
  }

  /**
   * 视频剪切处理
   */
  static async cutVideo(options: VideoProcessOptions & { startTime: number; endTime: number }): Promise<boolean> {
    return new Promise((resolve, reject) => {
      try {
        console.log(`开始视频剪切处理: ${options.inputPath}`)
        console.log(`剪切时间: ${options.startTime}s - ${options.endTime}s`)
        
        const duration = options.endTime - options.startTime
        
        const command = ffmpeg(options.inputPath)
          .seekInput(options.startTime) // 从指定时间开始
          .duration(duration) // 持续时间
          .videoCodec('libx264') // 视频编码
          .audioCodec('aac') // 音频编码
          .output(options.outputPath)
          .on('start', (commandLine) => {
            console.log('FFmpeg命令:', commandLine)
          })
          .on('progress', (progress) => {
            const percent = Math.round(progress.percent || 0)
            console.log(`处理进度: ${percent}%`)
            options.onProgress?.(percent)
          })
          .on('end', () => {
            console.log('视频剪切处理完成')
            options.onComplete?.()
            resolve(true)
          })
          .on('error', (err) => {
            console.error('视频剪切处理失败:', err.message)
            options.onError?.(err.message)
            reject(new Error(`视频剪切处理失败: ${err.message}`))
          })

        // 开始处理
        command.run()
      } catch (error) {
        console.error('视频剪切处理异常:', error)
        reject(error)
      }
    })
  }

  /**
   * 获取视频信息
   */
  static async getVideoInfo(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          console.error('获取视频信息失败:', err)
          reject(err)
          return
        }

        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video')
        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio')

        const info = {
          duration: metadata.format.duration || 0,
          size: metadata.format.size || 0,
          bitrate: metadata.format.bit_rate || 0,
          video: videoStream ? {
            width: videoStream.width || 0,
            height: videoStream.height || 0,
            codec: videoStream.codec_name || '',
            fps: eval(videoStream.r_frame_rate || '0/1')
          } : null,
          audio: audioStream ? {
            codec: audioStream.codec_name || '',
            sampleRate: audioStream.sample_rate || 0,
            channels: audioStream.channels || 0
          } : null
        }

        resolve(info)
      })
    })
  }

  /**
   * 操作类型到中文名称的映射
   */
  private static operationNames: Record<string, string> = {
    'mute': '视频静音',
    'extract_audio': '视频转音频',
    'replace_audio': '更换背景音乐',
    'compress': '视频压缩',
    'change_speed': '视频调速',
    'cut_video': '视频剪切'
  }

  /**
   * 生成输出文件路径
   */
  static generateOutputPath(inputPath: string, outputDir: string, operation: string, extension?: string): string {
    const inputName = path.basename(inputPath, path.extname(inputPath))
    const inputExt = extension || path.extname(inputPath)
    const operationName = this.operationNames[operation] || operation
    const outputName = `${inputName}_${operationName}${inputExt}`
    return path.join(outputDir, outputName)
  }
}