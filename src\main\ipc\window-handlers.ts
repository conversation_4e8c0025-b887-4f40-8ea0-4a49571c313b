import { ipcMain } from 'electron'
import { WINDOW_EVENTS } from '../constants/ipc-events'
import * as windowService from '../services/window-service'

/**
 * 注册所有窗口相关的IPC处理器
 */
export const registerWindowHandlers = () => {
  ipcMain.on(WINDOW_EVENTS.MINIMIZE, windowService.minimizeWindow)
  ipcMain.on(WINDOW_EVENTS.MAXIMIZE, windowService.toggleMaximizeWindow)
  ipcMain.on(WINDOW_EVENTS.CLOSE, windowService.closeWindow)
}

/**
 * 移除所有窗口相关的IPC处理器
 */
export const unregisterWindowHandlers = () => {
  ipcMain.removeAllListeners(WINDOW_EVENTS.MINIMIZE)
  ipcMain.removeAllListeners(WINDOW_EVENTS.MAXIMIZE)
  ipcMain.removeAllListeners(WINDOW_EVENTS.CLOSE)
}