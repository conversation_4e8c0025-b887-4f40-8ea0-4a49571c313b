import { useState } from 'react'
import { Checkbox, Slider, Input } from 'antd'
import FeaturePage from '../components/FeaturePage'
import { VIDEO_OPERATIONS } from '../services/file-service'

function ChangeSpeedPage() {
  const [speedMultiplier, setSpeedMultiplier] = useState(1.0)
  const [maintainPitch, setMaintainPitch] = useState(true)
  const [customSpeed, setCustomSpeed] = useState('')

  const presetSpeeds = [
    { label: '0.25x (极慢)', value: 0.25 },
    { label: '0.5x (慢速)', value: 0.5 },
    { label: '0.75x (稍慢)', value: 0.75 },
    { label: '1.0x (正常)', value: 1.0 },
    { label: '1.25x (稍快)', value: 1.25 },
    { label: '1.5x (快速)', value: 1.5 },
    { label: '2.0x (倍速)', value: 2.0 },
    { label: '自定义', value: -1 }
  ]

  const handleSpeedChange = (value: number) => {
    setSpeedMultiplier(value)
    if (value !== -1) {
      setCustomSpeed('')
    }
  }

  return (
    <FeaturePage
      title="视频调速"
      icon="⚡"
      description="调整视频播放速度，支持加速、减速和自定义倍率"
      operation={VIDEO_OPERATIONS.CHANGE_SPEED}
    >
      <div className="space-y-6">
        <div>
          <label className="block text-white/90 text-sm font-medium mb-3">
            选择播放速度
          </label>
          <div className="grid grid-cols-2 gap-2">
            {presetSpeeds.map((speed) => (
              <button
                key={speed.value}
                onClick={() => handleSpeedChange(speed.value)}
                className={`p-3 rounded-lg text-sm font-medium transition-all duration-200 ${speedMultiplier === speed.value
                    ? 'bg-purple-500 text-white'
                    : 'bg-white/10 text-white/80 hover:bg-white/20'
                  }`}
              >
                {speed.label}
              </button>
            ))}
          </div>
        </div>

        {speedMultiplier === -1 && (
          <div>
            <label className="block text-white/90 text-sm font-medium mb-2">
              自定义速度倍率
            </label>
            <Input
              type="number"
              value={customSpeed}
              onChange={(e) => setCustomSpeed(e.target.value)}
              placeholder="例如: 1.75"
              min="0.1"
              max="10"
              step="0.1"
              size="large"
            />
            <p className="text-xs text-white/60 mt-1">
              支持 0.1x - 10x 之间的任意倍率
            </p>
          </div>
        )}

        <div>
          <label className="block text-white/90 text-sm font-medium mb-3">
            速度调节滑块
          </label>
          <Slider
            min={0.25}
            max={3}
            step={0.25}
            value={speedMultiplier === -1 ? 1 : speedMultiplier}
            onChange={handleSpeedChange}
            marks={{
              0.25: '0.25x',
              1: '1.0x',
              3: '3.0x'
            }}
            tooltip={{ formatter: (value) => `${value}x` }}
          />
        </div>

        <Checkbox
          checked={maintainPitch}
          onChange={(e) => setMaintainPitch(e.target.checked)}
        >
          <span className="text-white/90">保持音调不变 (推荐)</span>
        </Checkbox>

        <div className="bg-white/5 rounded-lg p-4">
          <h4 className="text-white/90 font-medium mb-2">效果预览</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-white/60">当前速度:</span>
              <span className="text-white ml-2">
                {speedMultiplier === -1 ? (customSpeed || '1.0') : speedMultiplier}x
              </span>
            </div>
            <div>
              <span className="text-white/60">原始时长:</span>
              <span className="text-white ml-2">-- 分钟</span>
            </div>
            <div>
              <span className="text-white/60">调速后时长:</span>
              <span className="text-white ml-2">-- 分钟</span>
            </div>
            <div>
              <span className="text-white/60">文件大小变化:</span>
              <span className="text-white ml-2">约 --%</span>
            </div>
          </div>
        </div>

        <div className="text-sm text-white/60">
          <p>• 支持音频和视频同步调速</p>
          <p>• 可选择保持或改变音调</p>
          <p>• 适用于教学、娱乐等多种场景</p>
        </div>
      </div>
    </FeaturePage>
  )
}

export default ChangeSpeedPage