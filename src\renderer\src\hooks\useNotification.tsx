import { useCallback } from 'react'
import { notification, Modal } from 'antd'

export function useNotification() {
  const [api, contextHolder] = notification.useNotification()

  // 便捷方法
  const showSuccess = useCallback((title: string, message: string, onClose?: () => void) => {
    api.success({
      message: title,
      description: message,
      placement: 'topRight',
      duration: 3,
      onClose
    })
  }, [api])

  const showError = useCallback((title: string, message: string, onClose?: () => void) => {
    api.error({
      message: title,
      description: message,
      placement: 'topRight',
      duration: 4,
      onClose
    })
  }, [api])

  const showWarning = useCallback((title: string, message: string, onClose?: () => void) => {
    api.warning({
      message: title,
      description: message,
      placement: 'topRight',
      duration: 3,
      onClose
    })
  }, [api])

  const showInfo = useCallback((title: string, message: string, onClose?: () => void) => {
    api.info({
      message: title,
      description: message,
      placement: 'topRight',
      duration: 3,
      onClose
    })
  }, [api])

  const showConfirm = useCallback((
    title: string, 
    message: string, 
    onConfirm: () => void, 
    onCancel?: () => void,
    confirmText = '确定',
    cancelText = '取消'
  ) => {
    Modal.confirm({
      title,
      content: message,
      okText: confirmText,
      cancelText,
      centered: true,
      onOk: onConfirm,
      onCancel,
      okButtonProps: {
        style: {
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          borderColor: 'transparent'
        }
      }
    })
  }, [])

  return {
    contextHolder,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirm
  }
}