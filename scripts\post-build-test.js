const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('=== 打包后测试 ===');

// 查找打包后的应用
const distPath = path.join(__dirname, '..', 'dist');
const winPath = path.join(distPath, 'win-unpacked');

if (!fs.existsSync(winPath)) {
  console.log('❌ 未找到打包后的应用，请先运行: npm run build:unpack');
  process.exit(1);
}

console.log('✅ 找到打包后的应用:', winPath);

// 检查 FFmpeg 文件
const ffmpegPaths = [
  path.join(winPath, 'resources', 'app.asar.unpacked', 'node_modules', '@ffmpeg-installer', 'win32-x64', 'ffmpeg.exe'),
  path.join(winPath, 'resources', 'app.asar.unpacked', 'node_modules', '@ffmpeg-installer', 'ffmpeg', 'ffmpeg.exe'),
  path.join(winPath, 'node_modules', '@ffmpeg-installer', 'win32-x64', 'ffmpeg.exe'),
  path.join(winPath, 'node_modules', '@ffmpeg-installer', 'ffmpeg', 'ffmpeg.exe'),
];

console.log('\n检查 FFmpeg 文件:');
let ffmpegFound = false;
ffmpegPaths.forEach(p => {
  const exists = fs.existsSync(p);
  console.log(`${exists ? '✅' : '❌'} ${p}`);
  if (exists) ffmpegFound = true;
});

if (!ffmpegFound) {
  console.log('\n❌ 未找到 FFmpeg 文件！');
  
  // 列出实际的目录结构
  console.log('\n实际的目录结构:');
  try {
    const resourcesPath = path.join(winPath, 'resources');
    if (fs.existsSync(resourcesPath)) {
      console.log('resources 目录内容:');
      fs.readdirSync(resourcesPath).forEach(item => {
        console.log(`  - ${item}`);
      });
      
      const asarUnpackedPath = path.join(resourcesPath, 'app.asar.unpacked');
      if (fs.existsSync(asarUnpackedPath)) {
        console.log('\napp.asar.unpacked 目录内容:');
        fs.readdirSync(asarUnpackedPath).forEach(item => {
          console.log(`  - ${item}`);
        });
        
        const nodeModulesPath = path.join(asarUnpackedPath, 'node_modules');
        if (fs.existsSync(nodeModulesPath)) {
          console.log('\nnode_modules 目录内容:');
          fs.readdirSync(nodeModulesPath).forEach(item => {
            console.log(`  - ${item}`);
          });
        }
      }
    }
  } catch (error) {
    console.log('读取目录结构失败:', error.message);
  }
} else {
  console.log('\n✅ FFmpeg 文件已正确打包！');
}

// 启动应用进行测试（可选）
const exePath = path.join(winPath, '视频剪辑.exe');
if (fs.existsSync(exePath)) {
  console.log('\n✅ 找到可执行文件:', exePath);
  console.log('可以手动启动应用进行测试');
} else {
  console.log('\n❌ 未找到可执行文件');
}
