#!/usr/bin/env node

/**
 * 构建信息生成脚本
 * 在构建时生成版本信息，包括Git提交信息和构建时间
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

function getGitInfo() {
  try {
    const gitCommit = execSync('git rev-parse HEAD', { encoding: 'utf8' }).trim()
    const gitBranch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf8' }).trim()
    return { gitCommit, gitBranch }
  } catch (error) {
    console.warn('无法获取Git信息:', error.message)
    return { gitCommit: undefined, gitBranch: undefined }
  }
}

function generateBuildInfo() {
  const packageJson = require('../package.json')
  const { gitCommit, gitBranch } = getGitInfo()
  const buildTime = new Date().toISOString()
  const buildDate = buildTime.split('T')[0]

  const buildInfo = {
    version: packageJson.version,
    buildDate,
    buildTime,
    gitCommit,
    gitBranch,
    environment: process.env.NODE_ENV || 'development'
  }

  // 生成构建信息文件
  const buildInfoPath = path.join(__dirname, '../src/shared/build-info.json')
  fs.writeFileSync(buildInfoPath, JSON.stringify(buildInfo, null, 2))

  console.log('构建信息已生成:')
  console.log(`- 版本: ${buildInfo.version}`)
  console.log(`- 构建时间: ${buildInfo.buildTime}`)
  console.log(`- Git提交: ${buildInfo.gitCommit || '未知'}`)
  console.log(`- Git分支: ${buildInfo.gitBranch || '未知'}`)
  console.log(`- 环境: ${buildInfo.environment}`)

  return buildInfo
}

// 如果直接运行此脚本
if (require.main === module) {
  generateBuildInfo()
}

module.exports = { generateBuildInfo }
