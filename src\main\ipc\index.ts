import { registerWindowHandlers, unregisterWindowHandlers } from './window-handlers'
import { registerAppHandlers, unregisterAppHandlers } from './app-handlers'
import { registerFileHandlers, unregisterFileHandlers } from './file-handlers'

/**
 * 注册所有IPC处理器
 */
export const registerAllIpcHandlers = () => {
  registerWindowHandlers()
  registerAppHandlers()
  registerFileHandlers()
}

/**
 * 移除所有IPC处理器
 */
export const unregisterAllIpcHandlers = () => {
  unregisterWindowHandlers()
  unregisterAppHandlers()
  unregisterFileHandlers()
}

// 导出各个模块的处理器，方便单独使用
export { registerWindowHandlers, unregisterWindowHandlers } from './window-handlers'
export { registerAppHandlers, unregisterAppHandlers } from './app-handlers'
export { registerFileHandlers, unregisterFileHandlers } from './file-handlers'
