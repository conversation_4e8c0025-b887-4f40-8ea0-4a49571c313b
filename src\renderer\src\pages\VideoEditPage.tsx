import { useState, useEffect } from 'react'
import { Button, Card, InputNumber, Space, Divider, Switch } from 'antd'
import { ScissorOutlined } from '@ant-design/icons'
import FeaturePage from '../components/FeaturePage'
import VideoEditor from '../components/VideoEditor'
import AdvancedVideoEditor from '../components/AdvancedVideoEditor'
import { VIDEO_OPERATIONS } from '../services/file-service'

function VideoEditPage() {
  const [startTime, setStartTime] = useState(0)
  const [endTime, setEndTime] = useState(10)
  const [selectedVideo, setSelectedVideo] = useState<string | null>(null)
  const [useAdvancedEditor, setUseAdvancedEditor] = useState(false) // 先默认使用简单编辑器

  // 处理视频剪切
  const handleCut = (start: number, end: number) => {
    setStartTime(start)
    setEndTime(end)
  }

  // 提供处理选项
  const getProcessOptions = () => {
    const options = {
      startTime,
      endTime,
      operation: 'cut'
    }
    console.log('VideoEditPage - getProcessOptions:', options)
    return options
  }

  // 验证是否可以开始处理
  const validateBeforeProcess = () => {
    if (startTime >= endTime) {
      return { valid: false, message: '开始时间必须小于结束时间' }
    }
    if (endTime - startTime < 0.1) {
      return { valid: false, message: '剪切片段至少需要0.1秒' }
    }
    return { valid: true }
  }

  return (
    <FeaturePage
      title="视频剪辑"
      icon="✂️"
      description="精确剪切视频片段，支持可视化时间轴编辑"
      operation={VIDEO_OPERATIONS.CUT_VIDEO}
      getProcessOptions={getProcessOptions}
      validateBeforeProcess={validateBeforeProcess}
      enableLiveEdit={true}
    >
      {(selectedVideoFile) => (
        <div className="space-y-6">
          {/* 编辑器选择 */}
          <Card title="编辑器模式" className="bg-gray-800 border-gray-600">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-medium">
                  {useAdvancedEditor ? '专业音轨编辑器' : '简单编辑器'}
                </div>
                <div className="text-white/60 text-sm">
                  {useAdvancedEditor 
                    ? '支持音频波形可视化、精确拖拽选择和专业编辑功能' 
                    : '基础的时间轴编辑和预览功能'
                  }
                </div>
              </div>
              <Switch
                checked={useAdvancedEditor}
                onChange={setUseAdvancedEditor}
                checkedChildren="专业"
                unCheckedChildren="简单"
                disabled={!selectedVideoFile}
              />
            </div>
          </Card>

          {/* 视频文件状态 */}
          {!selectedVideoFile && (
            <Card className="bg-yellow-500/10 border-yellow-500/20">
              <div className="text-center py-8">
                <div className="text-yellow-400 text-lg mb-2">📁</div>
                <div className="text-yellow-400 font-medium">请先选择视频文件</div>
                <div className="text-yellow-400/70 text-sm mt-1">
                  拖拽视频文件到上方区域或点击选择文件
                </div>
              </div>
            </Card>
          )}

          {/* 视频编辑器组件 */}
          {selectedVideoFile && (
            <>
              {useAdvancedEditor ? (
                <AdvancedVideoEditor 
                  videoFile={selectedVideoFile} 
                  onCut={handleCut} 
                />
              ) : (
                <VideoEditor 
                  videoFile={selectedVideoFile} 
                  onCut={handleCut} 
                />
              )}
            </>
          )}

          {/* 精确时间控制 */}
          <Card title="精确时间设置" className="bg-gray-800 border-gray-600">
          <div className="grid grid-cols-2 gap-6">
            <div>
              <label className="block text-white/90 text-sm font-medium mb-2">
                开始时间 (秒)
              </label>
              <InputNumber
                min={0}
                step={0.1}
                precision={1}
                value={startTime}
                onChange={(value) => setStartTime(value || 0)}
                className="w-full"
                placeholder="0.0"
              />
            </div>
            
            <div>
              <label className="block text-white/90 text-sm font-medium mb-2">
                结束时间 (秒)
              </label>
              <InputNumber
                min={0.1}
                step={0.1}
                precision={1}
                value={endTime}
                onChange={(value) => setEndTime(value || 0.1)}
                className="w-full"
                placeholder="10.0"
              />
            </div>
          </div>

          <Divider />

          <div className="text-center">
            <Space size="large">
              <div className="text-white/70">
                <div className="text-sm">剪切时长</div>
                <div className="text-lg font-bold text-blue-400">
                  {(endTime - startTime).toFixed(1)} 秒
                </div>
              </div>
              
              <div className="text-white/70">
                <div className="text-sm">开始时间</div>
                <div className="text-lg font-bold text-green-400">
                  {startTime.toFixed(1)}s
                </div>
              </div>
              
              <div className="text-white/70">
                <div className="text-sm">结束时间</div>
                <div className="text-lg font-bold text-red-400">
                  {endTime.toFixed(1)}s
                </div>
              </div>
            </Space>
          </div>
        </Card>

        {/* 快捷操作 */}
        <Card title="快捷操作" className="bg-gray-800 border-gray-600">
          <Space wrap>
            <Button
              onClick={() => { setStartTime(0); setEndTime(30) }}
              size="small"
            >
              前30秒
            </Button>
            <Button
              onClick={() => { setStartTime(0); setEndTime(60) }}
              size="small"
            >
              前1分钟
            </Button>
            <Button
              onClick={() => { setStartTime(0); setEndTime(180) }}
              size="small"
            >
              前3分钟
            </Button>
            <Button
              onClick={() => { 
                const mid = (startTime + endTime) / 2
                setStartTime(Math.max(0, mid - 15))
                setEndTime(mid + 15)
              }}
              size="small"
            >
              中间30秒
            </Button>
          </Space>
        </Card>

          <div className="text-sm text-white/60">
            <p>• 支持精确到0.1秒的剪切</p>
            <p>• 可视化时间轴拖拽选择</p>
            <p>• 实时预览剪切效果</p>
            <p>• 保持原视频质量输出</p>
          </div>
        </div>
      )}
    </FeaturePage>
  )
}

export default VideoEditPage