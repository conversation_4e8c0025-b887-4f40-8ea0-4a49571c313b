import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { Select, Checkbox, Slider, Input, Button, Tooltip } from 'antd'
import { FolderOpenOutlined } from '@ant-design/icons'
import { ROUTES } from '../router'
import { useNotification } from '../hooks/useNotification'
import { useAppSettings } from '../hooks/useAppSettings'
import { useThemeContext } from '../components/ThemeProvider'
import * as appService from '../services/app-service'
import * as fileService from '../services/file-service'
import { OUTPUT_FOLDER_NAME } from '../constants/app-config'

function SettingsPage() {
  const navigate = useNavigate()
  const { contextHolder, showSuccess, showConfirm, showError } = useNotification()
  const { settings, isLoading, saveSettings, resetSettings, getFullOutputPath } = useAppSettings()
  const { themeMode, setThemeMode } = useThemeContext()
  const [versionInfo, setVersionInfo] = useState({
    version: 'Unknown',
    buildDate: 'Unknown',
    environment: 'Unknown',
    formattedVersion: 'Unknown'
  })

  // 获取详细版本信息
  useEffect(() => {
    const loadVersionInfo = async () => {
      try {
        const detailedInfo = await appService.getDetailedVersionInfo()
        if (detailedInfo) {
          setVersionInfo({
            version: detailedInfo.formattedVersion,
            buildDate: detailedInfo.formattedBuildDate,
            environment: detailedInfo.environment,
            formattedVersion: detailedInfo.formattedVersion
          })
        } else {
          // 如果获取详细信息失败，回退到基本版本信息
          const basicVersion = await appService.getAppVersion()
          setVersionInfo(prev => ({
            ...prev,
            version: basicVersion,
            formattedVersion: basicVersion
          }))
        }
      } catch (error) {
        console.error('获取版本信息失败:', error)
      }
    }
    loadVersionInfo()
  }, [])

  const handleSettingChange = async (key: string, value: any) => {
    // 保存设置到localStorage
    saveSettings({ [key]: value })
    
    // 根据设置类型执行相应的操作
    try {
      if (key === 'hardwareAcceleration') {
        try {
          const success = await appService.setHardwareAcceleration(value)
          if (success) {
            showSuccess('硬件加速设置已更新', '设置将在下次启动应用时生效')
          } else {
            showError('设置失败', '无法更新硬件加速设置')
          }
        } catch (error) {
          console.error('硬件加速设置失败:', error)
          // 即使API调用失败，设置也已保存到localStorage
          showSuccess('设置已保存', '硬件加速设置已保存，将在下次启动时生效')
        }
      } else if (key === 'maxConcurrentTasks') {
        try {
          const success = await appService.setMaxConcurrentTasks(value)
          if (success) {
            showSuccess('并发任务数已更新', `最大并发任务数已设置为 ${value}`)
          } else {
            showError('设置失败', '无法更新最大并发任务数')
          }
        } catch (error) {
          console.error('并发任务数设置失败:', error)
          // 即使API调用失败，设置也已保存到localStorage
          showSuccess('设置已保存', `最大并发任务数设置已保存为 ${value}`)
        }
      } else if (key === 'theme') {
        // 主题切换
        setThemeMode(value)
        showSuccess('主题已切换', `已切换到${value === 'dark' ? '深色' : value === 'light' ? '浅色' : '跟随系统'}主题`)
      } else {
        // 其他设置直接显示成功
        showSuccess('设置已保存', '您的设置已成功保存')
      }
    } catch (error) {
      console.error('应用设置失败:', error)
      // 即使出现错误，设置也已保存到localStorage
      showSuccess('设置已保存', '设置已保存到本地存储')
    }
  }

  const handleSelectOutputPath = async () => {
    try {
      const folderPath = await fileService.selectOutputFolder()
      if (folderPath) {
        saveSettings({ outputPath: folderPath })
        showSuccess('输出路径已设置', `输出路径已设置为: ${folderPath}/${OUTPUT_FOLDER_NAME}`)
      }
    } catch (error) {
      console.error('选择输出路径失败:', error)
      showError('选择失败', '无法选择输出路径，请重试')
    }
  }

  const handleSaveSettings = () => {
    showSuccess('设置已保存', '您的设置已成功保存并生效')
  }

  const handleResetSettings = () => {
    showConfirm(
      '重置设置',
      '确定要重置所有设置到默认值吗？此操作无法撤销。',
      async () => {
        try {
          const success = await resetSettings()
          if (success) {
            showSuccess('重置完成', '所有设置已重置为默认值')
          } else {
            showError('重置失败', '重置设置时出现错误，请重试')
          }
        } catch (error) {
          console.error('重置设置失败:', error)
          showError('重置失败', '重置设置时出现错误，请重试')
        }
      }
    )
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <header className="flex items-center justify-between px-6 py-4" style={{ backgroundColor: 'var(--bg-overlay)', backdropFilter: 'blur(8px)' }}>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => navigate(ROUTES.HOME)}
            className="transition-colors"
            style={{ color: 'var(--text-secondary)' }}
            onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
            onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>
          <div className="flex items-center space-x-3">
            <div className="text-3xl">⚙️</div>
            <h1 className="text-2xl font-bold" style={{ color: 'var(--text-primary)' }}>设置</h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <div className="flex-1 p-8 overflow-y-auto">
        <div className="max-w-4xl mx-auto space-y-6">
          
          {/* 输出设置 */}
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">输出设置</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-white/90 text-sm font-medium mb-2">
                  默认输出路径
                </label>
                <div className="flex space-x-2">
                  <Input
                    value={isLoading ? '' : settings.outputPath}
                    placeholder={isLoading ? "获取默认桌面路径中..." : "选择输出文件夹..."}
                    readOnly
                    size="large"
                    className="flex-1"
                    suffix={isLoading ? (
                      <div className="animate-spin w-4 h-4 border-2 border-purple-500 border-t-transparent rounded-full"></div>
                    ) : null}
                  />
                  <Button
                    onClick={handleSelectOutputPath}
                    type="primary"
                    size="large"
                    disabled={isLoading}
                    loading={isLoading}
                  >
                    浏览
                  </Button>
                </div>
                {settings.outputPath && (
                  <div className="mt-2 p-3 bg-white/5 rounded-lg">
                    <div className="flex items-center justify-between mb-1">
                      <p className="text-white/70 text-sm">完整输出路径:</p>
                      <Tooltip title="打开输出文件夹">
                        <Button
                          type="text"
                          size="small"
                          icon={<FolderOpenOutlined />}
                          onClick={async () => {
                            try {
                              const fullPath = getFullOutputPath()
                              if (fullPath) {
                                const result = await appService.openFolder(fullPath)
                                if (!result.success) {
                                  showError('打开失败', result.error || '无法打开输出文件夹')
                                } else {
                                  showSuccess('已打开', '输出文件夹已在文件管理器中打开')
                                }
                              }
                            } catch (error) {
                              console.error('打开文件夹失败:', error)
                              showError('打开失败', '无法打开输出文件夹')
                            }
                          }}
                          className="text-white/70 hover:text-white"
                        />
                      </Tooltip>
                    </div>
                    <p className="text-white/90 text-sm font-mono break-all">
                      {getFullOutputPath()}
                    </p>
                    <p className="text-white/60 text-xs mt-1">
                      处理后的文件将保存在 "{OUTPUT_FOLDER_NAME}" 文件夹中
                    </p>
                    {settings.outputPath.includes('Desktop') && (
                      <p className="text-green-400 text-xs mt-1">
                        ✅ 已自动设置为桌面路径（默认）
                      </p>
                    )}
                  </div>
                )}
              </div>

              {/* <Checkbox
                checked={settings.autoOpenOutput}
                onChange={(e) => handleSettingChange('autoOpenOutput', e.target.checked)}
              >
                <span className="text-white/90">处理完成后自动打开输出文件夹</span>
              </Checkbox> */}
            </div>
          </div>

          {/* 性能设置 */}
          {/* <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">性能设置</h3>
            
            <div className="space-y-4">
              <Checkbox
                checked={settings.hardwareAcceleration}
                onChange={(e) => handleSettingChange('hardwareAcceleration', e.target.checked)}
              >
                <span className="text-white/90">启用硬件加速 (推荐)</span>
              </Checkbox>

              <div>
                <label className="block text-white/90 text-sm font-medium mb-2">
                  最大并发任务数: {settings.maxConcurrentTasks}
                </label>
                <Slider
                  min={1}
                  max={8}
                  value={settings.maxConcurrentTasks}
                  onChange={(value) => handleSettingChange('maxConcurrentTasks', value)}
                  marks={{
                    1: '1',
                    4: '4',
                    8: '8'
                  }}
                  tooltip={{ formatter: (value) => `${value} 个任务` }}
                />
              </div>
            </div>
          </div> */}

          {/* 界面设置 */}
          <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">界面设置</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-white/90 text-sm font-medium mb-2">
                  主题
                </label>
                <Select
                  value={themeMode}
                  onChange={(value) => handleSettingChange('theme', value)}
                  className="w-full"
                  size="large"
                  options={[
                    { value: 'dark', label: '深色主题' },
                    { value: 'light', label: '浅色主题' },
                    { value: 'auto', label: '跟随系统' }
                  ]}
                />
              </div>
            </div>
          </div>

          {/* 关于 */}
          {/* <div className="bg-white/5 backdrop-blur-sm rounded-2xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">关于</h3>
            
            <div className="space-y-3 text-sm text-white/80">
              <div className="flex justify-between">
                <span>应用版本:</span>
                <span>{versionInfo.formattedVersion}</span>
              </div>
              <div className="flex justify-between">
                <span>构建日期:</span>
                <span>{versionInfo.buildDate}</span>
              </div>
              <div className="flex justify-between">
                <span>运行环境:</span>
                <span>{versionInfo.environment === 'development' ? '开发版' : '正式版'}</span>
              </div>
              <div className="flex justify-between">
                <span>开发者:</span>
                <span>VideoClip Team</span>
              </div>
            </div>

            <div className="mt-4 pt-4 border-t border-white/10">
              <button className="text-purple-400 hover:text-purple-300 text-sm transition-colors">
                检查更新
              </button>
            </div>
          </div> */}

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={handleSaveSettings}
              className="flex-1 py-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-xl font-semibold hover:from-purple-600 hover:to-pink-600 transition-all duration-300"
            >
              保存设置
            </button>
            <button
              onClick={handleResetSettings}
              className="px-6 py-3 bg-white/10 text-white rounded-xl font-semibold hover:bg-white/20 transition-all duration-300 border border-white/20"
            >
              重置
            </button>
          </div>
        </div>
      </div>

      {/* Antd Notification Context */}
      {contextHolder}
    </div>
  )
}

export default SettingsPage