import React, { useRef, useEffect, useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Card } from 'antd'
import { PlayCircleOutlined, PauseCircleOutlined, ScissorOutlined } from '@ant-design/icons'

interface VideoEditorProps {
  videoFile?: string
  onCut?: (startTime: number, endTime: number) => void
}

const VideoEditor: React.FC<VideoEditorProps> = ({ videoFile, onCut }) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [startTime, setStartTime] = useState(0)
  const [endTime, setEndTime] = useState(0)
  const [audioData, setAudioData] = useState<number[]>([])

  // 初始化视频
  useEffect(() => {
    const loadVideo = async () => {
      if (videoFile && videoRef.current) {
        console.log('Loading video file:', videoFile)

        try {
          // 使用安全的文件 URL API
          const videoUrl = await window.electronAPI?.file.getFileUrl(videoFile)
          if (videoUrl) {
            console.log('Video URL:', videoUrl)
            videoRef.current.src = videoUrl

            // 添加加载成功的处理
            videoRef.current.onloadeddata = () => {
              console.log('Video data loaded successfully')
            }

            videoRef.current.oncanplaythrough = () => {
              console.log('Video can play through')
            }
          } else {
            console.error('Failed to get video URL from electronAPI')
            // 备用方案：直接使用文件路径（仅在开发环境）
            if (process.env.NODE_ENV === 'development') {
              console.log('Trying direct file path as fallback')
              videoRef.current.src = videoFile
            }
          }
        } catch (error) {
          console.error('Error getting video URL:', error)
          // 备用方案：直接使用文件路径
          console.log('Trying direct file path as fallback')
          videoRef.current.src = videoFile
        }

        // 添加详细的错误处理
        videoRef.current.onerror = (e) => {
          console.error('Video loading error:', e)
          console.error('Video error details:', videoRef.current?.error)
          if (videoRef.current?.error) {
            const error = videoRef.current.error
            console.error('Error code:', error.code)
            console.error('Error message:', error.message)

            // 根据错误类型提供更具体的信息
            switch (error.code) {
              case MediaError.MEDIA_ERR_ABORTED:
                console.error('视频加载被中止')
                break
              case MediaError.MEDIA_ERR_NETWORK:
                console.error('网络错误导致视频加载失败')
                break
              case MediaError.MEDIA_ERR_DECODE:
                console.error('视频解码错误')
                break
              case MediaError.MEDIA_ERR_SRC_NOT_SUPPORTED:
                console.error('视频格式不支持或文件路径无效')
                break
              default:
                console.error('未知的视频加载错误')
            }
          }
        }

        videoRef.current.onloadstart = () => {
          console.log('Video loading started')
        }

        videoRef.current.oncanplay = () => {
          console.log('Video can play')
        }

        videoRef.current.onprogress = () => {
          console.log('Video loading progress')
        }
      }
    }

    loadVideo()
  }, [videoFile])

  // 视频加载完成
  const handleLoadedMetadata = () => {
    if (videoRef.current) {
      const videoDuration = videoRef.current.duration
      setDuration(videoDuration)
      setEndTime(videoDuration)
      generateWaveform()
    }
  }

  // 生成音频波形
  const generateWaveform = async () => {
    if (!videoRef.current) return

    try {
      // 这里可以使用 Web Audio API 分析音频
      // 简化版本：生成模拟波形数据
      const samples = 1000
      const mockData = Array.from({ length: samples }, () => Math.random() * 100)
      setAudioData(mockData)
    } catch (error) {
      console.error('生成波形失败:', error)
    }
  }

  // 绘制波形
  useEffect(() => {
    if (canvasRef.current && audioData.length > 0) {
      const canvas = canvasRef.current
      const ctx = canvas.getContext('2d')
      if (!ctx) return

      const width = canvas.width
      const height = canvas.height
      
      ctx.clearRect(0, 0, width, height)
      
      // 绘制波形
      ctx.fillStyle = '#1890ff'
      const barWidth = width / audioData.length
      
      audioData.forEach((value, index) => {
        const barHeight = (value / 100) * height * 0.8
        const x = index * barWidth
        const y = (height - barHeight) / 2
        
        ctx.fillRect(x, y, barWidth - 1, barHeight)
      })

      // 绘制选择区域
      const startX = (startTime / duration) * width
      const endX = (endTime / duration) * width
      
      ctx.fillStyle = 'rgba(24, 144, 255, 0.3)'
      ctx.fillRect(startX, 0, endX - startX, height)

      // 绘制当前播放位置
      const currentX = (currentTime / duration) * width
      ctx.strokeStyle = '#ff4d4f'
      ctx.lineWidth = 2
      ctx.beginPath()
      ctx.moveTo(currentX, 0)
      ctx.lineTo(currentX, height)
      ctx.stroke()
    }
  }, [audioData, startTime, endTime, currentTime, duration])

  // 播放/暂停
  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause()
      } else {
        videoRef.current.play()
      }
      setIsPlaying(!isPlaying)
    }
  }

  // 时间更新
  const handleTimeUpdate = () => {
    if (videoRef.current) {
      setCurrentTime(videoRef.current.currentTime)
    }
  }

  // 拖拽时间轴
  const handleCanvasClick = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!canvasRef.current || !videoRef.current) return

    const rect = canvasRef.current.getBoundingClientRect()
    const x = e.clientX - rect.left
    const clickTime = (x / rect.width) * duration

    videoRef.current.currentTime = clickTime
    setCurrentTime(clickTime)
  }

  // 执行剪切
  const handleCut = () => {
    if (onCut) {
      onCut(startTime, endTime)
    }
  }

  return (
    <div className="video-editor space-y-4">
      <Card title="视频预览" className="bg-gray-800 border-gray-600">
        <video
          ref={videoRef}
          className="w-full max-h-96 bg-black rounded"
          controls
          onLoadedMetadata={handleLoadedMetadata}
          onTimeUpdate={handleTimeUpdate}
          onPlay={() => setIsPlaying(true)}
          onPause={() => setIsPlaying(false)}
        />
        
        {/* 调试信息 */}
        {videoFile && (
          <div className="mt-2 p-2 bg-gray-700 rounded text-xs text-white/70">
            <div>文件路径: {videoFile}</div>
            <div>时长: {duration.toFixed(1)}s</div>
            <div>当前时间: {currentTime.toFixed(1)}s</div>
          </div>
        )}
        
        <div className="flex items-center justify-center mt-4 space-x-4">
          <Button
            type="primary"
            icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
            onClick={togglePlay}
          >
            {isPlaying ? '暂停' : '播放'}
          </Button>
          
          <span className="text-white">
            {Math.floor(currentTime)}s / {Math.floor(duration)}s
          </span>
        </div>
      </Card>

      <Card title="音轨编辑" className="bg-gray-800 border-gray-600">
        <div className="space-y-4">
          <canvas
            ref={canvasRef}
            width={800}
            height={120}
            className="w-full border border-gray-600 cursor-pointer"
            onClick={handleCanvasClick}
          />
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-white text-sm mb-2">开始时间 (秒)</label>
              <Slider
                min={0}
                max={duration}
                step={0.1}
                value={startTime}
                onChange={setStartTime}
                tooltip={{ formatter: (value) => `${value?.toFixed(1)}s` }}
              />
            </div>
            
            <div>
              <label className="block text-white text-sm mb-2">结束时间 (秒)</label>
              <Slider
                min={0}
                max={duration}
                step={0.1}
                value={endTime}
                onChange={setEndTime}
                tooltip={{ formatter: (value) => `${value?.toFixed(1)}s` }}
              />
            </div>
          </div>

          <div className="flex justify-center">
            <Button
              type="primary"
              icon={<ScissorOutlined />}
              size="large"
              onClick={handleCut}
              disabled={startTime >= endTime}
            >
              剪切视频 ({(endTime - startTime).toFixed(1)}秒)
            </Button>
          </div>
        </div>
      </Card>
    </div>
  )
}

export default VideoEditor