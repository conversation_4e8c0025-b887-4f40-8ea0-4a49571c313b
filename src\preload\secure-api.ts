import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { IPC_EVENTS } from '../main/constants/ipc-events'

// 安全的API定义 - 只暴露需要的功能
const secureAPI = {
  // 窗口控制
  window: {
    minimize: () => ipcRenderer.send(IPC_EVENTS.MINIMIZE),
    maximize: () => ipcRenderer.send(IPC_EVENTS.MAXIMIZE),
    close: () => ipcRenderer.send(IPC_EVENTS.CLOSE)
  },

  // 文件操作
  file: {
    selectVideo: (): Promise<string | null> => ipcRenderer.invoke(IPC_EVENTS.SELECT_FILE),
    processVideo: (filePath: string, operation: string): Promise<boolean> =>
      ipcRenderer.invoke(IPC_EVENTS.PROCESS_VIDEO, filePath, operation)
  },

  // 应用功能
  app: {
    ping: () => ipcRenderer.send(IPC_EVENTS.PING),
    getVersion: (): Promise<string> => ipcRenderer.invoke('app-get-version')
  }
}

// 类型定义
export type SecureAPI = typeof secureAPI

// 暴露安全API
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('secureAPI', secureAPI)
  } catch (error) {
    console.error('Failed to expose secure API:', error)
  }
} else {
  // @ts-ignore
  window.secureAPI = secureAPI
}

// 扩展全局类型
declare global {
  interface Window {
    secureAPI: SecureAPI
  }
}
