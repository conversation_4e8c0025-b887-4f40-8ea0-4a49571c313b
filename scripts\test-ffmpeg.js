const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
const { existsSync } = require('fs');
const path = require('path');

console.log('=== FFmpeg 安装测试 ===');
console.log('FFmpeg 路径:', ffmpegInstaller.path);
console.log('文件是否存在:', existsSync(ffmpegInstaller.path));

if (existsSync(ffmpegInstaller.path)) {
  console.log('✅ FFmpeg 安装成功！');
} else {
  console.log('❌ FFmpeg 未找到！');
  
  // 尝试查找可能的路径
  const possiblePaths = [
    path.join(__dirname, '..', 'node_modules', '@ffmpeg-installer', 'ffmpeg', 'ffmpeg.exe'),
    path.join(__dirname, '..', 'node_modules', '@ffmpeg-installer', 'ffmpeg', 'ffmpeg'),
    path.join(process.cwd(), 'node_modules', '@ffmpeg-installer', 'ffmpeg', 'ffmpeg.exe'),
    path.join(process.cwd(), 'node_modules', '@ffmpeg-installer', 'ffmpeg', 'ffmpeg'),
  ];
  
  console.log('\n尝试查找其他可能的路径:');
  possiblePaths.forEach(p => {
    console.log(`${existsSync(p) ? '✅' : '❌'} ${p}`);
  });
}

// 测试 fluent-ffmpeg
try {
  const ffmpeg = require('fluent-ffmpeg');
  ffmpeg.setFfmpegPath(ffmpegInstaller.path);
  
  ffmpeg.getAvailableFormats((err, formats) => {
    if (err) {
      console.log('❌ FFmpeg 测试失败:', err.message);
    } else {
      console.log('✅ FFmpeg 功能测试成功！');
      console.log('支持的格式数量:', Object.keys(formats).length);
    }
  });
} catch (error) {
  console.log('❌ fluent-ffmpeg 测试失败:', error.message);
}
