{"name": "videoclip", "version": "1.0.0", "description": "An Electron application with React and TypeScript", "main": "./out/main/index.js", "author": "example.com", "homepage": "https://electron-vite.org", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "prebuild": "node scripts/build-info.js", "test:version": "node scripts/test-version.js", "test:ffmpeg": "node scripts/test-ffmpeg.js", "test:build": "node scripts/post-build-test.js", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run prebuild && electron-vite build && electron-builder --mac", "build:linux": "npm run prebuild && electron-vite build && electron-builder --linux"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@ant-design/v5-patch-for-react-19": "^1.0.3", "@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@ffmpeg-installer/ffmpeg": "^1.1.0", "antd": "^5.26.6", "clsx": "^2.1.1", "electron-updater": "^6.3.9", "fluent-ffmpeg": "^2.1.3", "react-router-dom": "^7.7.1", "tailwindcss": "3.4.17", "uuid": "^11.1.0", "wavesurfer.js": "^7.10.1"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@types/fluent-ffmpeg": "^2.1.27", "@types/node": "^22.16.5", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-router-dom": "^5.3.3", "@types/wavesurfer.js": "^6.0.12", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "electron": "^37.2.3", "electron-builder": "^25.1.8", "electron-vite": "^4.0.0", "eslint": "^9.31.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "postcss": "^8.5.6", "prettier": "^3.6.2", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "vite": "^7.0.5"}}