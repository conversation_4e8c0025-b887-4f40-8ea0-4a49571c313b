import { useState } from 'react'
import { Select, Checkbox, Input } from 'antd'
import FeaturePage from '../components/FeaturePage'
import { VIDEO_OPERATIONS } from '../services/file-service'

function CompressVideoPage() {
  const [compressionLevel, setCompressionLevel] = useState('medium')
  const [targetSize, setTargetSize] = useState('')
  const [maintainQuality, setMaintainQuality] = useState(true)

  return (
    <FeaturePage
      title="视频压缩"
      icon="📦"
      description="智能压缩视频文件大小，保持画质的同时减少存储空间"
      operation={VIDEO_OPERATIONS.COMPRESS}
    >
      <div className="space-y-6">
        <div>
          <label className="block text-white/90 text-sm font-medium mb-2">
            压缩级别
          </label>
          <Select
            value={compressionLevel}
            onChange={setCompressionLevel}
            className="w-full"
            size="large"
            options={[
              { value: 'light', label: '轻度压缩 (保持高画质)' },
              { value: 'medium', label: '中度压缩 (平衡画质与大小)' },
              { value: 'heavy', label: '重度压缩 (最小文件大小)' },
              { value: 'custom', label: '自定义设置' }
            ]}
          />
        </div>

        {compressionLevel === 'custom' && (
          <div>
            <label className="block text-white/90 text-sm font-medium mb-2">
              目标文件大小 (MB)
            </label>
            <Input
              type="number"
              value={targetSize}
              onChange={(e) => setTargetSize(e.target.value)}
              placeholder="例如: 50"
              size="large"
            />
          </div>
        )}

        <Checkbox
          checked={maintainQuality}
          onChange={(e) => setMaintainQuality(e.target.checked)}
        >
          <span className="text-white/90">优先保持画质</span>
        </Checkbox>

        <div className="bg-white/5 rounded-lg p-4">
          <h4 className="text-white/90 font-medium mb-2">预估效果</h4>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-white/60">原始大小:</span>
              <span className="text-white ml-2">-- MB</span>
            </div>
            <div>
              <span className="text-white/60">压缩后:</span>
              <span className="text-white ml-2">-- MB</span>
            </div>
            <div>
              <span className="text-white/60">压缩率:</span>
              <span className="text-white ml-2">--%</span>
            </div>
            <div>
              <span className="text-white/60">预计时间:</span>
              <span className="text-white ml-2">-- 分钟</span>
            </div>
          </div>
        </div>

        <div className="text-sm text-white/60">
          <p>• 使用先进的H.264/H.265编码</p>
          <p>• 智能分析视频内容优化参数</p>
          <p>• 支持批量压缩处理</p>
        </div>
      </div>
    </FeaturePage>
  )
}

export default CompressVideoPage