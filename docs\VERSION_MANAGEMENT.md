# 版本管理系统

本项目实现了一套完整的版本一致化管理系统，确保应用的版本信息在各个地方保持一致。

## 系统架构

### 1. 版本信息来源
- **package.json**: 主要版本号来源
- **Git信息**: 提交哈希和分支信息
- **构建时间**: 自动生成的构建时间戳

### 2. 核心文件

#### `src/shared/version.ts`
统一的版本信息管理模块，提供：
- `getVersionInfo()`: 获取完整版本信息
- `formatVersion()`: 格式化版本显示
- `formatBuildDate()`: 格式化构建日期

#### `scripts/build-info.js`
构建信息生成脚本，在构建时自动：
- 获取Git提交信息
- 生成构建时间戳
- 创建`src/shared/build-info.json`文件

#### `src/shared/build-info.json`
构建时生成的版本信息文件（不提交到Git）：
```json
{
  "version": "1.0.0",
  "buildDate": "2025-07-26",
  "buildTime": "2025-07-26T03:52:20.822Z",
  "gitCommit": "6435eafc7af708cbb6c78e21af917ec4f61e3b1d",
  "gitBranch": "master",
  "environment": "development"
}
```

### 3. API层次

#### 主进程 (Main Process)
- `src/main/services/app-service.ts`: 提供版本信息服务
- `src/main/ipc/app-handlers.ts`: IPC处理器
- `src/main/constants/ipc-events.ts`: 事件常量

#### 预加载脚本 (Preload)
- `src/preload/index.ts`: 安全API暴露

#### 渲染进程 (Renderer)
- `src/renderer/src/services/app-service.ts`: 前端服务

### 4. UI组件

#### `src/renderer/src/components/VersionBadge.tsx`
版本信息显示组件，支持：
- 简单版本显示
- 详细信息展示
- 开发/生产环境区分

#### 使用位置
- **主页**: 底部版本徽章
- **设置页**: 详细版本信息

## 使用方法

### 开发环境
```bash
# 启动开发服务器
npm run dev

# 生成构建信息（可选，开发时会自动生成）
node scripts/build-info.js
```

### 生产构建
```bash
# Windows构建
npm run build:win

# macOS构建
npm run build:mac

# Linux构建
npm run build:linux
```

构建脚本会自动：
1. 运行`prebuild`脚本生成版本信息
2. 编译应用
3. 打包为可执行文件

### 版本信息格式

#### 开发环境
- 显示: `v1.0.0-dev+6435eaf`
- 颜色: 橙色徽章

#### 生产环境
- 显示: `v1.0.0`
- 颜色: 绿色徽章

## 自定义配置

### 修改版本号
在`package.json`中修改`version`字段：
```json
{
  "version": "1.1.0"
}
```

### 添加新的版本信息
1. 在`src/shared/version.ts`中扩展`VersionInfo`接口
2. 更新`getVersionInfo()`函数
3. 在`scripts/build-info.js`中添加信息收集逻辑

### 自定义版本显示
修改`src/shared/version.ts`中的`formatVersion()`函数：
```typescript
export const formatVersion = (versionInfo: VersionInfo): string => {
  let version = `v${versionInfo.version}`
  
  // 添加自定义后缀
  if (versionInfo.environment === 'development') {
    version += '-dev'
  }
  
  // 添加Git信息
  if (versionInfo.gitCommit) {
    version += `+${versionInfo.gitCommit.substring(0, 7)}`
  }
  
  return version
}
```

## 故障排除

### 构建信息文件不存在
如果`src/shared/build-info.json`不存在，系统会回退到默认值：
- 版本号从`package.json`读取
- 构建时间使用当前时间
- Git信息从环境变量读取

### Git信息获取失败
在没有Git环境或CI/CD中，Git信息可能无法获取：
- 脚本会显示警告但继续执行
- 版本信息中Git字段为`undefined`
- 不影响应用正常运行

### 类型错误
确保所有相关文件的类型定义保持同步：
- `src/preload/index.d.ts`: Preload API类型定义
- `src/shared/version.ts`: 版本信息类型
- `src/preload/index.ts`: Preload实现

## 最佳实践

1. **版本号管理**: 使用语义化版本控制 (SemVer)
2. **构建信息**: 不要手动编辑`build-info.json`
3. **Git提交**: 确保提交信息清晰，因为会显示在版本信息中
4. **环境区分**: 利用环境标识区分开发和生产版本
5. **UI一致性**: 在所有需要显示版本的地方使用统一的组件

## 扩展功能

### 自动版本更新检查
可以基于版本信息实现自动更新检查：
```typescript
// 在设置页面添加
const checkForUpdates = async () => {
  const currentVersion = await appService.getDetailedVersionInfo()
  // 实现更新检查逻辑
}
```

### 版本历史记录
可以扩展系统记录版本变更历史：
```typescript
// 添加到版本信息中
interface VersionInfo {
  // ... 现有字段
  changelog?: string[]
  releaseNotes?: string
}
```

这套版本管理系统确保了应用版本信息的一致性和可维护性，为后续的功能扩展提供了良好的基础。