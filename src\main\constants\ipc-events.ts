// 窗口控制相关事件
export const WINDOW_EVENTS = {
  MINIMIZE: 'window-minimize',
  MAXIMIZE: 'window-maximize',
  CLOSE: 'window-close'
} as const

// 应用相关事件
export const APP_EVENTS = {
  PING: 'ping',
  GET_VERSION: 'app-get-version',
  OPEN_FOLDER: 'app-open-folder',
  SET_HARDWARE_ACCELERATION: 'app-set-hardware-acceleration',
  GET_SYSTEM_INFO: 'app-get-system-info',
  SET_MAX_CONCURRENT_TASKS: 'app-set-max-concurrent-tasks',
  GET_TASK_STATUS: 'app-get-task-status'
} as const

// 文件操作相关事件
export const FILE_EVENTS = {
  SELECT_FILE: 'file-select',
  SELECT_FILES: 'files-select',
  SELECT_AUDIO: 'audio-select',
  SELECT_FOLDER: 'folder-select',
  GET_DEFAULT_OUTPUT_PATH: 'get-default-output-path',
  ENSURE_OUTPUT_DIRECTORY: 'ensure-output-directory',
  PROCESS_VIDEO: 'video-process',
  GET_FILE_URL: 'get-file-url'
} as const

// 进度相关事件
export const PROGRESS_EVENTS = {
  PROGRESS_UPDATE: 'progress-update',
  PROGRESS_COMPLETE: 'progress-complete',
  PROGRESS_ERROR: 'progress-error'
} as const

// 导出所有事件常量
export const IPC_EVENTS = {
  ...WINDOW_EVENTS,
  ...APP_EVENTS,
  ...FILE_EVENTS,
  ...PROGRESS_EVENTS
} as const