/**
 * 选择视频文件 - 使用安全API
 */
export const selectVideoFile = async (): Promise<string | null> => {
  try {
    const filePath = await window.electronAPI?.file.selectVideo()
    return filePath || null
  } catch (error) {
    console.error('选择文件失败:', error)
    return null
  }
}

/**
 * 选择多个视频文件 - 使用安全API
 */
export const selectVideoFiles = async (): Promise<string[]> => {
  try {
    const filePaths = await window.electronAPI?.file.selectVideos()
    return filePaths || []
  } catch (error) {
    console.error('选择文件失败:', error)
    return []
  }
}

/**
 * 获取文件的真实路径 - 使用安全API
 */
export const getPathForFile = (file: File): string | null => {
  try {
    const filePath = window.electronAPI?.file.getPathForFile(file)
    return filePath || null
  } catch (error) {
    console.error('获取文件路径失败:', error)
    return null
  }
}

/**
 * 选择输出文件夹 - 使用安全API
 */
export const selectOutputFolder = async (): Promise<string | null> => {
  try {
    const folderPath = await window.electronAPI?.file.selectFolder()
    return folderPath || null
  } catch (error) {
    console.error('选择文件夹失败:', error)
    return null
  }
}

/**
 * 获取默认输出路径（用户桌面）- 使用安全API
 */
export const getDefaultOutputPath = async (): Promise<string> => {
  try {
    const defaultPath = await window.electronAPI?.file.getDefaultOutputPath()
    return defaultPath || ''
  } catch (error) {
    console.error('获取默认输出路径失败:', error)
    return ''
  }
}

/**
 * 确保输出目录存在 - 使用安全API
 */
export const ensureOutputDirectory = async (
  outputPath: string,
  folderName: string
): Promise<boolean> => {
  try {
    const result = await window.electronAPI?.file.ensureOutputDirectory(outputPath, folderName)
    return result || false
  } catch (error) {
    console.error('创建输出目录失败:', error)
    return false
  }
}

/**
 * 处理视频文件 - 使用安全API
 */
export const processVideo = async (
  filePath: string,
  operation: string,
  outputPath?: string,
  outputFolderName?: string,
  options?: any
): Promise<boolean> => {
  try {
    const result = await window.electronAPI?.file.processVideo(
      filePath,
      operation,
      outputPath,
      outputFolderName,
      options
    )
    return result || false
  } catch (error) {
    console.error('处理视频失败:', error)
    return false
  }
}

/**
 * 视频操作类型
 */
export const VIDEO_OPERATIONS = {
  MUTE: 'mute', // 静音
  EXTRACT_AUDIO: 'extract_audio', // 提取音频
  REPLACE_AUDIO: 'replace_audio', // 替换音频
  COMPRESS: 'compress', // 压缩
  CHANGE_SPEED: 'change_speed', // 调速
  CUT_VIDEO: 'cut_video' // 视频剪切
} as const

export type VideoOperation = (typeof VIDEO_OPERATIONS)[keyof typeof VIDEO_OPERATIONS]
