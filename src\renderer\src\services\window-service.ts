/**
 * 窗口控制服务 - 使用安全API
 */

/**
 * 最小化窗口
 */
export const minimizeWindow = (): void => {
  try {
    window.electronAPI?.window.minimize()
  } catch (error) {
    console.error('最小化窗口失败:', error)
  }
}

/**
 * 最大化/还原窗口
 */
export const maximizeWindow = (): void => {
  try {
    window.electronAPI?.window.maximize()
  } catch (error) {
    console.error('最大化窗口失败:', error)
  }
}

/**
 * 关闭窗口
 */
export const closeWindow = (): void => {
  try {
    window.electronAPI?.window.close()
  } catch (error) {
    console.error('关闭窗口失败:', error)
  }
}