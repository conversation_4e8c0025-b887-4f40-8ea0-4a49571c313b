@import './base.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS变量定义 */
:root {
  /* 深色主题变量 */
  --bg-gradient-start: #667eea;
  --bg-gradient-end: #764ba2;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --bg-overlay: rgba(255, 255, 255, 0.1);
  --bg-overlay-hover: rgba(255, 255, 255, 0.2);
}

/* 浅色主题变量 */
:root.light {
  --bg-gradient-start: #e0e7ff;
  --bg-gradient-end: #c7d2fe;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --bg-overlay: rgba(0, 0, 0, 0.05);
  --bg-overlay-hover: rgba(0, 0, 0, 0.1);
}

/* 跟随系统主题 */
@media (prefers-color-scheme: light) {
  :root.auto {
    --bg-gradient-start: #e0e7ff;
    --bg-gradient-end: #c7d2fe;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --bg-overlay: rgba(0, 0, 0, 0.05);
    --bg-overlay-hover: rgba(0, 0, 0, 0.1);
  }
}

@media (prefers-color-scheme: dark) {
  :root.auto {
    --bg-gradient-start: #667eea;
    --bg-gradient-end: #764ba2;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --bg-overlay: rgba(255, 255, 255, 0.1);
    --bg-overlay-hover: rgba(255, 255, 255, 0.2);
  }
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
  min-height: 100vh;
  overflow: hidden;
  user-select: none;
  transition: background 0.3s ease;
}

#root {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 主题过渡动画 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
