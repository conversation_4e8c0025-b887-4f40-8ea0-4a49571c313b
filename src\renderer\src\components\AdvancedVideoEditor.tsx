import React, { useRef, useEffect, useState, useCallback } from 'react'
import { <PERSON><PERSON>, Card, Slider, Space, Tooltip, message } from 'antd'
import {
  PlayCircleOutlined,
  PauseCircleOutlined,
  ScissorOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  ReloadOutlined
} from '@ant-design/icons'
import WaveSurfer from 'wavesurfer.js'
import RegionsPlugin from 'wavesurfer.js/dist/plugins/regions.js'

// 类型定义
interface Region {
  start: number
  end: number
  color?: string
  drag?: boolean
  resize?: boolean
}

interface AdvancedVideoEditorProps {
  videoFile?: string
  onCut?: (startTime: number, endTime: number) => void
  onTimeUpdate?: (currentTime: number) => void
}

const AdvancedVideoEditor: React.FC<AdvancedVideoEditorProps> = ({
  videoFile,
  onCut,
  onTimeUpdate
}) => {
  const videoRef = useRef<HTMLVideoElement>(null)
  const waveformRef = useRef<HTMLDivElement>(null)
  const wavesurferRef = useRef<WaveSurfer | null>(null)
  const regionsPluginRef = useRef<any>(null)

  const [isPlaying, setIsPlaying] = useState(false)
  const [duration, setDuration] = useState(0)
  const [currentTime, setCurrentTime] = useState(0)
  const [volume, setVolume] = useState(50)
  const [playbackRate, setPlaybackRate] = useState(1)
  const [selectedRegion, setSelectedRegion] = useState<{ start: number, end: number } | null>(null)
  const [zoom, setZoom] = useState(1)

  // 初始化 WaveSurfer
  const initWaveSurfer = useCallback(async () => {
    if (!waveformRef.current || !videoFile) return

    try {
      // 清理之前的实例
      if (wavesurferRef.current) {
        wavesurferRef.current.destroy()
      }

      // 创建 regions 插件
      regionsPluginRef.current = RegionsPlugin.create()

      // 创建 WaveSurfer 实例
      wavesurferRef.current = WaveSurfer.create({
        container: waveformRef.current,
        waveColor: '#4f46e5',
        progressColor: '#06b6d4',
        cursorColor: '#ef4444',
        barWidth: 2,
        barRadius: 3,
        responsive: true,
        height: 80,
        normalize: true,
        plugins: [regionsPluginRef.current]
      })

      // 获取安全的文件 URL 并加载音频
      const audioUrl = await window.electronAPI?.file.getFileUrl(videoFile)
      if (audioUrl) {
        await wavesurferRef.current.load(audioUrl)
      } else {
        throw new Error('无法获取音频文件 URL')
      }

      // 监听事件
      wavesurferRef.current.on('ready', () => {
        const audioDuration = wavesurferRef.current?.getDuration() || 0
        setDuration(audioDuration)

        // 创建默认选择区域
        if (regionsPluginRef.current && audioDuration > 0) {
          const defaultRegion = regionsPluginRef.current.addRegion({
            start: 0,
            end: Math.min(30, audioDuration),
            color: 'rgba(59, 130, 246, 0.3)',
            drag: true,
            resize: true
          })

          setSelectedRegion({
            start: 0,
            end: Math.min(30, audioDuration)
          })
        }
      })

      wavesurferRef.current.on('audioprocess', (time: number) => {
        setCurrentTime(time)
        onTimeUpdate?.(time)

        // 同步视频播放位置
        if (videoRef.current && Math.abs(videoRef.current.currentTime - time) > 0.5) {
          videoRef.current.currentTime = time
        }
      })

      wavesurferRef.current.on('play', () => {
        setIsPlaying(true)
        if (videoRef.current) {
          videoRef.current.play()
        }
      })

      wavesurferRef.current.on('pause', () => {
        setIsPlaying(false)
        if (videoRef.current) {
          videoRef.current.pause()
        }
      })

      // 监听区域变化
      regionsPluginRef.current.on('region-updated', (region: any) => {
        setSelectedRegion({
          start: region.start,
          end: region.end
        })
      })

    } catch (error) {
      console.error('初始化 WaveSurfer 失败:', error)
      message.error('音频波形加载失败')
    }
  }, [videoFile, onTimeUpdate])

  // 初始化视频
  useEffect(() => {
    const loadVideo = async () => {
      if (videoFile && videoRef.current) {
        try {
          // 使用安全的文件 URL API
          const videoUrl = await window.electronAPI?.file.getFileUrl(videoFile)
          if (videoUrl) {
            console.log('Video URL:', videoUrl)
            videoRef.current.src = videoUrl
          } else {
            console.error('Failed to get video URL')
          }
        } catch (error) {
          console.error('Error getting video URL:', error)
        }
      }
    }

    loadVideo()
  }, [videoFile])

  // 初始化 WaveSurfer
  useEffect(() => {
    initWaveSurfer()

    return () => {
      if (wavesurferRef.current) {
        wavesurferRef.current.destroy()
      }
    }
  }, [initWaveSurfer])

  // 播放/暂停控制
  const togglePlay = () => {
    if (wavesurferRef.current) {
      wavesurferRef.current.playPause()
    }
  }

  // 跳转到指定时间
  const seekTo = (time: number) => {
    if (wavesurferRef.current) {
      wavesurferRef.current.seekTo(time / duration)
    }
  }

  // 音量控制
  const handleVolumeChange = (value: number) => {
    setVolume(value)
    if (wavesurferRef.current) {
      wavesurferRef.current.setVolume(value / 100)
    }
    if (videoRef.current) {
      videoRef.current.volume = value / 100
    }
  }

  // 播放速度控制
  const handlePlaybackRateChange = (rate: number) => {
    setPlaybackRate(rate)
    if (wavesurferRef.current) {
      wavesurferRef.current.setPlaybackRate(rate)
    }
    if (videoRef.current) {
      videoRef.current.playbackRate = rate
    }
  }

  // 缩放控制
  const handleZoom = (zoomLevel: number) => {
    setZoom(zoomLevel)
    if (wavesurferRef.current) {
      wavesurferRef.current.zoom(zoomLevel)
    }
  }

  // 重置波形
  const resetWaveform = () => {
    if (wavesurferRef.current) {
      wavesurferRef.current.seekTo(0)
      setCurrentTime(0)
    }
  }

  // 执行剪切
  const handleCut = () => {
    if (selectedRegion && onCut) {
      onCut(selectedRegion.start, selectedRegion.end)
      message.success(`已选择剪切片段: ${selectedRegion.start.toFixed(1)}s - ${selectedRegion.end.toFixed(1)}s`)
    } else {
      message.warning('请先选择要剪切的区域')
    }
  }

  // 格式化时间显示
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <div className="advanced-video-editor space-y-4">
      {/* 视频预览 */}
      <Card title="视频预览" className="bg-gray-800 border-gray-600">
        <video
          ref={videoRef}
          className="w-full max-h-96 bg-black rounded"
          muted // 静音，因为音频由 WaveSurfer 控制
        />
      </Card>

      {/* 音频波形编辑器 */}
      <Card title="音轨编辑器" className="bg-gray-800 border-gray-600">
        <div className="space-y-4">
          {/* 波形显示区域 */}
          <div
            ref={waveformRef}
            className="w-full border border-gray-600 rounded bg-gray-900"
            style={{ minHeight: '80px' }}
          />

          {/* 控制面板 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* 播放控制 */}
            <div className="space-y-2">
              <label className="block text-white text-sm font-medium">播放控制</label>
              <Space>
                <Button
                  type="primary"
                  icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                  onClick={togglePlay}
                >
                  {isPlaying ? '暂停' : '播放'}
                </Button>
                <Button icon={<ReloadOutlined />} onClick={resetWaveform}>
                  重置
                </Button>
              </Space>
              <div className="text-white text-sm">
                {formatTime(currentTime)} / {formatTime(duration)}
              </div>
            </div>

            {/* 音量控制 */}
            <div className="space-y-2">
              <label className="block text-white text-sm font-medium">音量: {volume}%</label>
              <Slider
                min={0}
                max={100}
                value={volume}
                onChange={handleVolumeChange}
                tooltip={{ formatter: (value) => `${value}%` }}
              />
            </div>

            {/* 播放速度 */}
            <div className="space-y-2">
              <label className="block text-white text-sm font-medium">播放速度: {playbackRate}x</label>
              <Slider
                min={0.25}
                max={2}
                step={0.25}
                value={playbackRate}
                onChange={handlePlaybackRateChange}
                marks={{
                  0.25: '0.25x',
                  0.5: '0.5x',
                  1: '1x',
                  1.5: '1.5x',
                  2: '2x'
                }}
                tooltip={{ formatter: (value) => `${value}x` }}
              />
            </div>
          </div>

          {/* 缩放和剪切控制 */}
          <div className="flex justify-between items-center">
            <Space>
              <Tooltip title="放大波形">
                <Button
                  icon={<ZoomInOutlined />}
                  onClick={() => handleZoom(Math.min(zoom * 2, 100))}
                  disabled={zoom >= 100}
                />
              </Tooltip>
              <Tooltip title="缩小波形">
                <Button
                  icon={<ZoomOutOutlined />}
                  onClick={() => handleZoom(Math.max(zoom / 2, 1))}
                  disabled={zoom <= 1}
                />
              </Tooltip>
              <span className="text-white text-sm">缩放: {zoom}x</span>
            </Space>

            <Space>
              {selectedRegion && (
                <div className="text-white text-sm">
                  选中: {selectedRegion.start.toFixed(1)}s - {selectedRegion.end.toFixed(1)}s
                  ({(selectedRegion.end - selectedRegion.start).toFixed(1)}s)
                </div>
              )}
              <Button
                type="primary"
                icon={<ScissorOutlined />}
                onClick={handleCut}
                disabled={!selectedRegion}
                size="large"
              >
                剪切选中区域
              </Button>
            </Space>
          </div>
        </div>
      </Card>

      {/* 使用说明 */}
      <Card title="使用说明" className="bg-gray-800 border-gray-600">
        <div className="text-white/70 text-sm space-y-1">
          <p>• 在波形图上拖拽选择要剪切的区域</p>
          <p>• 可以拖拽区域边缘来调整选择范围</p>
          <p>• 使用缩放功能来精确选择时间点</p>
          <p>• 播放时视频和音频会同步</p>
          <p>• 支持变速播放来精确定位</p>
        </div>
      </Card>
    </div>
  )
}

export default AdvancedVideoEditor