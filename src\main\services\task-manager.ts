/**
 * 任务队列管理器
 * 控制并发任务数量，避免系统资源过载
 */

import { VideoProcessor } from './video-processor'
import { updateTaskProgress } from './progress-manager'
import { BrowserWindow } from 'electron'
import { IPC_EVENTS } from '../constants/ipc-events'
import * as path from 'path'
import { promises as fs } from 'fs'
import { v4 as uuidv4 } from 'uuid'

interface Task {
  id: string
  filePath: string
  operation: string
  outputPath?: string
  outputFolderName?: string
  // 额外参数，用于特殊操作
  audioPath?: string
  volume?: number
  speed?: number
  quality?: string
  startTime?: number
  endTime?: number
  resolve: (value: boolean) => void
  reject: (error: any) => void
}

class TaskManager {
  private queue: Task[] = []
  private running: Map<string, Task> = new Map()
  private maxConcurrent: number = 2

  /**
   * 设置最大并发任务数
   */
  setMaxConcurrent(max: number) {
    this.maxConcurrent = Math.max(1, Math.min(max, 8)) // 限制在1-8之间
    console.log(`设置最大并发任务数: ${this.maxConcurrent}`)
    this.processQueue()
  }

  /**
   * 获取当前状态
   */
  getStatus() {
    return {
      maxConcurrent: this.maxConcurrent,
      running: this.running.size,
      queued: this.queue.length,
      runningTasks: Array.from(this.running.values()).map((task) => ({
        id: task.id,
        filePath: task.filePath,
        operation: task.operation
      }))
    }
  }

  /**
   * 添加任务到队列
   */
  async addTask(
    filePath: string,
    operation: string,
    outputPath?: string,
    outputFolderName?: string,
    options?: {
      audioPath?: string
      volume?: number
      speed?: number
      quality?: string
      startTime?: number
      endTime?: number
    }
  ): Promise<boolean> {
    return new Promise((resolve, reject) => {
      console.log(`TaskManager - addTask options:`, options)
      
      const task: Task = {
        id: `task_${uuidv4()}`,
        filePath,
        operation,
        outputPath,
        outputFolderName,
        // 添加额外参数
        audioPath: options?.audioPath,
        volume: options?.volume,
        speed: options?.speed,
        quality: options?.quality,
        startTime: options?.startTime,
        endTime: options?.endTime,
        resolve,
        reject
      }

      console.log(`TaskManager - created task:`, {
        id: task.id,
        operation: task.operation,
        audioPath: task.audioPath,
        volume: task.volume
      })

      this.queue.push(task)
      console.log(`任务已添加到队列: ${task.id} (${operation})`)
      this.processQueue()
    })
  }

  /**
   * 处理队列中的任务
   */
  private processQueue() {
    // 如果当前运行的任务数已达到最大值，不处理新任务
    if (this.running.size >= this.maxConcurrent) {
      return
    }

    // 如果队列为空，没有任务需要处理
    if (this.queue.length === 0) {
      return
    }

    // 从队列中取出任务并开始执行
    const task = this.queue.shift()!
    this.running.set(task.id, task)

    console.log(`开始执行任务: ${task.id} (${task.operation})`)
    this.executeTask(task)
  }

  /**
   * 执行单个任务
   */
  private async executeTask(task: Task) {
    try {
      // 这里调用实际的视频处理逻辑，传递完整的任务对象
      const success = await this.processVideo(task)

      // 任务完成，从运行列表中移除
      this.running.delete(task.id)
      console.log(`任务完成: ${task.id} (${success ? '成功' : '失败'})`)

      // 解析Promise
      task.resolve(success)

      // 处理队列中的下一个任务
      this.processQueue()
    } catch (error) {
      // 任务失败，从运行列表中移除
      this.running.delete(task.id)
      console.error(`任务失败: ${task.id}`, error)

      // 拒绝Promise
      task.reject(error)

      // 处理队列中的下一个任务
      this.processQueue()
    }
  }

  /**
   * 实际的视频处理逻辑
   */
  private async processVideo(task: Task): Promise<boolean> {
    const { filePath, operation, outputPath, outputFolderName } = task
    console.log(`处理视频文件: ${filePath}, 操作: ${operation}`)

    // 确保输出目录存在
    if (!outputPath || !outputFolderName) {
      throw new Error('输出路径和文件夹名称不能为空')
    }

    const fullOutputPath = path.join(outputPath, outputFolderName)
    try {
      await fs.access(fullOutputPath)
    } catch (error) {
      // 目录不存在，创建它
      await fs.mkdir(fullOutputPath, { recursive: true })
      console.log(`创建输出目录: ${fullOutputPath}`)
    }

    // 生成输出文件路径
    const outputFilePath = VideoProcessor.generateOutputPath(filePath, fullOutputPath, operation)
    console.log(`输出文件将保存到: ${outputFilePath}`)

    // 根据操作类型调用相应的处理方法
    try {
      switch (operation) {
        case 'mute':
          return await VideoProcessor.muteVideo({
            inputPath: filePath,
            outputPath: outputFilePath,
            operation,
            onProgress: (progress) => {
              // 获取当前任务ID
              const taskId = this.getCurrentTaskId(filePath, operation)
              if (taskId) {
                // 更新进度到进度管理器
                updateTaskProgress(taskId, progress, 'processing')
                // 发送进度更新到渲染进程
                this.sendProgressUpdate(taskId, progress, 'processing')
              }
              console.log(`${operation} 进度: ${progress}%`)
            }
          })

        case 'extract_audio':
          // 对于音频提取，改变输出文件扩展名为.mp3
          const audioOutputPath = outputFilePath.replace(path.extname(outputFilePath), '.mp3')
          return await VideoProcessor.extractAudio({
            inputPath: filePath,
            outputPath: audioOutputPath,
            operation,
            audioFormat: 'mp3',
            onProgress: (progress) => {
              // 获取当前任务ID
              const taskId = this.getCurrentTaskId(filePath, operation)
              if (taskId) {
                // 更新进度到进度管理器
                updateTaskProgress(taskId, progress, 'processing')
                // 发送进度更新到渲染进程
                this.sendProgressUpdate(taskId, progress, 'processing')
              }
              console.log(`${operation} 进度: ${progress}%`)
            }
          })

        case 'compress':
          return await VideoProcessor.compressVideo({
            inputPath: filePath,
            outputPath: outputFilePath,
            operation,
            quality: 'medium',
            onProgress: (progress) => {
              // 获取当前任务ID
              const taskId = this.getCurrentTaskId(filePath, operation)
              if (taskId) {
                // 更新进度到进度管理器
                updateTaskProgress(taskId, progress, 'processing')
                // 发送进度更新到渲染进程
                this.sendProgressUpdate(taskId, progress, 'processing')
              }
              console.log(`${operation} 进度: ${progress}%`)
            }
          })

        case 'change_speed':
          return await VideoProcessor.changeVideoSpeed({
            inputPath: filePath,
            outputPath: outputFilePath,
            operation,
            speed: 1.5, // 默认1.5倍速
            onProgress: (progress) => {
              // 获取当前任务ID
              const taskId = this.getCurrentTaskId(filePath, operation)
              if (taskId) {
                // 更新进度到进度管理器
                updateTaskProgress(taskId, progress, 'processing')
                // 发送进度更新到渲染进程
                this.sendProgressUpdate(taskId, progress, 'processing')
              }
              console.log(`${operation} 进度: ${progress}%`)
            }
          })

        case 'replace_audio':
          if (!task.audioPath) {
            throw new Error('音频替换操作需要提供音频文件路径')
          }
          return await VideoProcessor.replaceAudio({
            inputPath: filePath,
            outputPath: outputFilePath,
            operation,
            audioPath: task.audioPath,
            volume: task.volume || 1.0,
            onProgress: (progress) => {
              // 获取当前任务ID
              const taskId = this.getCurrentTaskId(filePath, operation)
              if (taskId) {
                // 更新进度到进度管理器
                updateTaskProgress(taskId, progress, 'processing')
                // 发送进度更新到渲染进程
                this.sendProgressUpdate(taskId, progress, 'processing')
              }
              console.log(`${operation} 进度: ${progress}%`)
            }
          })

        case 'cut_video':
          // 视频剪切需要开始时间和结束时间
          const startTime = task.startTime || 0
          const endTime = task.endTime || 10
          return await VideoProcessor.cutVideo({
            inputPath: filePath,
            outputPath: outputFilePath,
            operation,
            startTime,
            endTime,
            onProgress: (progress) => {
              // 获取当前任务ID
              const taskId = this.getCurrentTaskId(filePath, operation)
              if (taskId) {
                // 更新进度到进度管理器
                updateTaskProgress(taskId, progress, 'processing')
                // 发送进度更新到渲染进程
                this.sendProgressUpdate(taskId, progress, 'processing')
              }
              console.log(`${operation} 进度: ${progress}%`)
            }
          })

        default:
          throw new Error(`不支持的操作类型: ${operation}`)
      }
    } catch (error) {
      console.error(`视频处理失败 (${operation}):`, error)
      throw error
    }
  }

  /**
   * 取消所有任务
   */
  cancelAllTasks() {
    // 清空队列
    while (this.queue.length > 0) {
      const task = this.queue.shift()!
      task.reject(new Error('任务已取消'))
    }

    console.log('所有排队任务已取消')
  }

  /**
   * 获取队列中的任务数量
   */
  getQueueLength(): number {
    return this.queue.length
  }

  /**
   * 获取正在运行的任务数量
   */
  getRunningCount(): number {
    return this.running.size
  }

  /**
   * 根据文件路径和操作获取当前任务ID
   */
  private getCurrentTaskId(filePath: string, operation: string): string | null {
    for (const [taskId, task] of this.running) {
      if (task.filePath === filePath && task.operation === operation) {
        return taskId
      }
    }
    return null
  }

  /**
   * 发送进度更新到渲染进程
   */
  private sendProgressUpdate(
    taskId: string,
    progress: number,
    status: 'processing' | 'completed' | 'error' = 'processing',
    message?: string
  ) {
    const mainWindow = BrowserWindow.getAllWindows()[0]
    if (mainWindow) {
      mainWindow.webContents.send(IPC_EVENTS.PROGRESS_UPDATE, {
        taskId,
        progress,
        status,
        message
      })
    }
  }
}

// 创建全局任务管理器实例
export const taskManager = new TaskManager()

// 导出任务管理器的方法
export const addVideoTask = taskManager.addTask.bind(taskManager)
export const setMaxConcurrentTasks = taskManager.setMaxConcurrent.bind(taskManager)
export const getTaskStatus = taskManager.getStatus.bind(taskManager)
export const cancelAllTasks = taskManager.cancelAllTasks.bind(taskManager)
